from collections import OrderedDict

from ..es import Provider as PersonProvider


class Provider(Person<PERSON>rovider):
    formats = (
        "{{first_name}} {{last_name}} {{last_name}}",
        "{{first_name}} {{first_name}} {{last_name}}",
        "{{first_name}} {{first_name}} {{last_name}} {{last_name}}",
        "{{first_name}} {{last_name}}",
        "{{prefix}} {{first_name}} {{last_name}}",
    )
    formats_female = (
        "{{first_names_female}} {{last_name}} {{last_name}}",
        "{{first_names_female}} {{first_names_female}} {{last_name}}",
        "{{first_names_female}} {{first_names_female}} {{last_name}} {{last_name}}",
        "{{first_names_female}} {{last_name}}",
        "{{prefix}} {{first_names_female}} {{last_name}}",
    )
    formats_male = (
        "{{first_names_male}} {{last_name}} {{last_name}}",
        "{{first_names_male}} {{first_names_male}} {{last_name}}",
        "{{first_names_male}} {{first_names_male}} {{last_name}} {{last_name}}",
        "{{first_names_male}} {{last_name}}",
        "{{prefix}} {{first_names_male}} {{last_name}}",
    )

    """
        Top 100 female names registered during 2010-2014
    """
    first_names_female = OrderedDict(
        [
            ("Martina", 0.0439534346041129),
            ("Isabella", 0.041382089943949055),
            ("Catalina", 0.03866702492610898),
            ("Sofia", 0.03018752382321146),
            ("Delfina", 0.02948141945723694),
            ("Emma", 0.02678822492860847),
            ("Valentina", 0.026463291946036128),
            ("Victoria", 0.02355764123649497),
            ("Emilia", 0.023554516880893314),
            ("Juana", 0.02178925596595702),
            ("Julieta", 0.02120812582404879),
            ("Josefina", 0.021101897733592445),
            ("Alma", 0.019539719932763863),
            ("Guadalupe", 0.019117931926540148),
            ("Olivia", 0.018358713515337462),
            ("Francesca", 0.018343091737329176),
            ("Camila", 0.017574500259321512),
            ("Lola", 0.017187080164716026),
            ("Renata", 0.016799660070110536),
            ("Malena", 0.014946917198327844),
            ("Jazmin", 0.014172077009116869),
            ("Pilar", 0.01407209762986384),
            ("Morena", 0.013481594421150636),
            ("Guillermina", 0.013144164016171665),
            ("Bianca", 0.012953578324470577),
            ("Agustina", 0.012884842501234119),
            ("Julia", 0.012138121512438057),
            ("Clara", 0.010666550024057538),
            ("Maria Victoria", 0.010241637662232163),
            ("Mia", 0.010066673748539362),
            ("Sofía", 0.009448051339411245),
            ("Mora", 0.00927933613692176),
            ("Ana Paula", 0.009207475958083645),
            ("Lucía", 0.009007517199577587),
            ("Mia Valentina", 0.007988977273437352),
            ("Lara", 0.007954609361819125),
            ("Ambar", 0.007723407047296495),
            ("Milagros", 0.007707785269288209),
            ("Antonella", 0.007707785269288209),
            ("Valentina ", 0.007654671224060036),
            ("Felicitas", 0.007642173801653409),
            ("Amparo", 0.007604681534433523),
            ("Agostina", 0.007539070066798722),
            ("Helena", 0.0072360075734379786),
            ("Juliana", 0.007073541082151805),
            ("Constanza", 0.006867333612442433),
            ("Lucia", 0.006742359388376147),
            ("Paloma", 0.006676747920741347),
            ("Luz Milagros", 0.0066736235651396894),
            ("Maria Paz", 0.006536151918666774),
            ("Abril", 0.006501784007048545),
            ("Maria Emilia", 0.006345566226965688),
            ("Zoe", 0.0061737266688745435),
            ("Matilda", 0.0061706023132728875),
            ("Paulina", 0.006070622934019858),
            ("Sofia Belen", 0.00527703661119894),
            ("Maia", 0.00525516612198734),
            ("Sol", 0.005239544343979053),
            ("Violeta", 0.005220798210369111),
            ("Lourdes", 0.005202052076759168),
            ("Luciana", 0.005161435453937625),
            ("Micaela", 0.005139564964726025),
            ("Alma Valentina", 0.0051176944755144255),
            ("Nina", 0.005073953497091225),
            ("Zoe Valentina", 0.005064580430286253),
            ("Sara", 0.004989595895846481),
            ("Antonia", 0.004923984428211681),
            ("Milena", 0.00479276149294208),
            ("Ludmila", 0.004786512781738766),
            ("Zoe Jazmin", 0.004724025669705622),
            ("Luana", 0.004664662913274137),
            ("Giuliana", 0.004589678378834366),
            ("Maite", 0.004580305312029394),
            ("Valentina Jazmin", 0.004549061756012822),
            ("Mia Jazmin", 0.0044896989995813355),
            ("Elena", 0.004480325932776364),
            ("Jazmín", 0.004427211887548193),
            ("Alma Mia", 0.004411590109539907),
            ("Candela", 0.0043990926871332785),
            ("Morena Jazmin", 0.004370973486718364),
            ("Joaquina", 0.004305362019083564),
            ("Tiziana", 0.004295988952278592),
            ("Luna", 0.00428661588547362),
            ("Francisca", 0.004274118463066992),
            ("Justina", 0.004224128773440478),
            ("Angelina", 0.004164766017008992),
            ("Uma", 0.00408978148256922),
            ("Kiara", 0.004042916148544362),
            ("Alfonsina", 0.004021045659332763),
            ("Florencia", 0.003971055969706248),
            ("Mia Morena", 0.003952309836096304),
            ("Luz Maria", 0.0039491854804946476),
            ("Uma ", 0.003883574012859847),
            ("Alma Jazmin", 0.00385857916804659),
            ("Carmela", 0.0038335843232333326),
            ("Isabel", 0.0037492267219885896),
            ("Paula", 0.003739853655183618),
            ("Maria Luz", 0.003699237032362075),
            ("Luisana", 0.0036929883211587605),
            ("Nahiara Jazmin", 0.003677366543150475),
        ]
    )

    """
    Top 100 registered names used during 2010-2014
    Weighted in conjuction with the female names for the top 250 most used names of those years.
    """
    first_names_male = OrderedDict(
        [
            ("Benjamin", 0.05313287990513444),
            ("Bautista", 0.03439693595213106),
            ("Santino", 0.03261957010645112),
            ("Juan Ignacio", 0.029013044011612837),
            ("Valentino", 0.02878678424904929),
            ("Felipe", 0.02801532023934465),
            ("Mateo", 0.027925361538566375),
            ("Joaquin", 0.02782177273160957),
            ("Santiago", 0.027393787397603817),
            ("Francisco", 0.02438425995338503),
            ("Thiago Benjamin", 0.021404718742758996),
            ("Juan Cruz", 0.017950849837120225),
            ("Ignacio", 0.01766734362860686),
            ("Lautaro", 0.01698856434091621),
            ("Benicio", 0.0168822495127237),
            ("Guadalupe", 0.016680523941281496),
            ("Thiago", 0.01611351152425477),
            ("Ciro", 0.014633281993266722),
            ("Pedro", 0.014526967165074211),
            ("Lorenzo", 0.014505158995188568),
            ("Simon", 0.013872722068504907),
            ("Tomàs", 0.013826379707497916),
            ("Facundo", 0.013068545803971808),
            ("Valentin", 0.01288862840241525),
            ("Agustin", 0.012572409939073421),
            ("Juan Bautista", 0.011364782531655918),
            ("Maximo", 0.011048564068314087),
            ("Bruno", 0.010983139558657158),
            ("Mateo Benjamin", 0.01049245573623018),
            ("Juan Pablo", 0.010435209290280368),
            ("Manuel", 0.009903635149317809),
            ("Nicolas", 0.009821854512246648),
            ("Lautaro Benjamin", 0.009794594299889593),
            ("Lucas", 0.009753703981354011),
            ("Tiziano", 0.009669197323047142),
            ("Franco", 0.009538348303733282),
            ("Dante", 0.009388417135769484),
            ("Salvador", 0.00891954148322815),
            ("Matias", 0.008641487317186197),
            ("Juan Martin", 0.008548802595172215),
            ("Tomas", 0.008191693813294803),
            ("Julian", 0.007968160071966957),
            ("Agustín", 0.0077173661182820584),
            ("Juan Manuel", 0.007398421633704524),
            ("Luca", 0.007281202720569191),
            ("Santino Benjamin", 0.007014052639470059),
            ("Jeremias", 0.006921367917456075),
            ("Vicente", 0.006678752027478291),
            ("Thiago Ezequiel", 0.006517916774571671),
            ("Ramiro", 0.006455218286150447),
            ("Luciano", 0.00644158817997192),
            ("Genaro", 0.006100835525508741),
            ("Federico", 0.005885479847888013),
            ("Thiago Agustin", 0.005790069104638323),
            ("Lucio", 0.005757356849809858),
            ("Joaquín", 0.005754630828574152),
            ("Lisandro", 0.005754630828574152),
            ("Ian Benjamin", 0.005730096637452804),
            ("Sebastian", 0.005713740510038571),
            ("Tiziano Benjamin", 0.005640137936674524),
            ("Thiago Nicolas", 0.005580165469489005),
            ("Thiago Valentin", 0.00551201493859637),
            ("Lautaro Ezequiel", 0.005427508280289502),
            ("Gonzalo", 0.005386617961753919),
            ("Alejo", 0.005364809791868277),
            ("Bautista Benjamin", 0.0051303719655976104),
            ("Camilo", 0.005086755625826323),
            ("Milo", 0.005048591328526447),
            ("Thiago Nahuel", 0.0049995229462837494),
            ("Octavio", 0.004996796925048044),
            ("Santiago Benjamin", 0.00490411220303406),
            ("Fausto", 0.004805975438548664),
            ("Martín", 0.004596071803399346),
            ("Francesco", 0.004579715675985114),
            ("Sol", 0.004571537612277998),
            ("Geronimo", 0.0045660855698065874),
            ("Juan", 0.004536099336213827),
            ("Benjamin Ezequiel", 0.0045224692300353),
            ("Tobias", 0.004320743658593099),
            ("Gael", 0.004309839573650277),
            ("Augusto", 0.0043970722531928505),
            ("Ezequiel", 0.0042089767879291765),
            ("Miguel Angel", 0.004110840023443781),
            ("Juan Gabriel", 0.003966360897951393),
            ("Thiago Leonel", 0.003952730791772867),
            ("Juan Francisco", 0.003941826706830045),
            ("Santino Ezequiel", 0.0037864434963948354),
            ("Mateo Ezequiel", 0.00378371747515913),
            ("Ian", 0.003868224133465998),
            ("Thiago Lionel", 0.0037291970504450215),
            ("Enzo", 0.0037182929655021997),
            ("Emiliano", 0.0037073888805593783),
            ("Lautaro Nicolas", 0.003674676625730913),
            ("Antonio", 0.003636512328431037),
            ("Tomas Benjamin", 0.0035983480311311614),
            ("Mateo Agustin", 0.003584717924952634),
            ("Luciano Benjamin", 0.0035465536276527584),
            ("Tiziano Valentin", 0.003494759224174355),
            ("Santino Nicolas", 0.003483855139231533),
            ("Alvaro", 0.003475677075524417),
        ]
    )

    """
    Top 250 names registered during 2010-2014
    Source: https://datos.gob.ar/dataset/otros-nombres-personas-fisicas/archivo/otros_2.20
    """
    first_names = OrderedDict(
        [
            ("Benjamin", 0.026454408864554677),
            ("Martina", 0.019093972803168394),
            ("Isabella", 0.01797694553440186),
            ("Bautista", 0.01712594177071217),
            ("Catalina", 0.01679748417770913),
            ("Santino", 0.01624100643749737),
            ("Juan Ignacio", 0.01444534777822869),
            ("Valentino", 0.014332694967405335),
            ("Felipe", 0.013948589600381119),
            ("Mateo", 0.013903799928607978),
            ("Joaquin", 0.013852223942929814),
            ("Santiago", 0.01363913368631214),
            ("Sofia", 0.01311387299006348),
            ("Delfina", 0.012807131601556509),
            ("Francisco", 0.012140715576083401),
            ("Emma", 0.011637171084330808),
            ("Valentina", 0.011496015755106361),
            ("Thiago Benjamin", 0.010657227356445708),
            ("Victoria", 0.01023376136877237),
            ("Emilia", 0.010232404105991365),
            ("Juana", 0.00946555063472394),
            ("Julieta", 0.009213099757457141),
            ("Josefina", 0.009166952822902995),
            ("Juan Cruz", 0.00893757541291327),
            ("Ignacio", 0.008796420083688822),
            ("Alma", 0.008488321432400848),
            ("Lautaro", 0.008458461651218755),
            ("Benicio", 0.008405528402759587),
            ("Guadalupe", 0.008305090956965269),
            ("Thiago", 0.008022780298516377),
            ("Olivia", 0.007975276101181226),
            ("Francesca", 0.007968489787276205),
            ("Camila", 0.007634603143149148),
            ("Lola", 0.007466302558304616),
            ("Renata", 0.0072980019734600835),
            ("Ciro", 0.007285786608431045),
            ("Pedro", 0.007232853359971878),
            ("Lorenzo", 0.007221995257723843),
            ("Simon", 0.006907110292530847),
            ("Tomàs", 0.006884036825253774),
            ("Facundo", 0.006506717772134581),
            ("Malena", 0.006493145144324538),
            ("Valentin", 0.006417138428588297),
            ("Agustin", 0.0062596959459918),
            ("Jazmin", 0.0061565439746354735),
            ("Pilar", 0.006113111565643336),
            ("Morena", 0.0058565889000335245),
            ("Guillermina", 0.005710004519685061),
            ("Juan Bautista", 0.005658428534006898),
            ("Bianca", 0.005627211490043799),
            ("Agustina", 0.005597351708861704),
            ("Maximo", 0.0055009860514104),
            ("Bruno", 0.005468411744666297),
            ("Julia", 0.005272965904201678),
            ("Mateo Benjamin", 0.005224104444085524),
            ("Juan Pablo", 0.005195601925684434),
            ("Manuel", 0.004930935683388597),
            ("Nicolas", 0.004890217799958468),
            ("Lautaro Benjamin", 0.004876645172148425),
            ("Lucas", 0.00485628623043336),
            ("Tiziano", 0.004814211084222227),
            ("Franco", 0.004749062470734021),
            ("Dante", 0.004674413017778785),
            ("Clara", 0.0046336951343486565),
            ("Maria Victoria", 0.004449107396132072),
            ("Salvador", 0.004440963819446047),
            ("Mia", 0.004373100680395832),
            ("Matias", 0.004302523015783609),
            ("Juan Martin", 0.004256376081229463),
            ("Sofía", 0.004104362649756982),
            ("Tomas", 0.004078574656917901),
            ("Mora", 0.004031070459582751),
            ("Ana Paula", 0.003999853415619652),
            ("Julian", 0.003967279108875548),
            ("Lucía", 0.003912988597635377),
            ("Agustín", 0.0038424109330231536),
            ("Juan Manuel", 0.0036836111876456515),
            ("Luca", 0.0036252488880624666),
            ("Santino Benjamin", 0.003492237135524046),
            ("Mia Valentina", 0.0034705209310279773),
            ("Lara", 0.00345559104043693),
            ("Jeremias", 0.0034460902009699),
            ("Ambar", 0.0033551535946426125),
            ("Milagros", 0.003348367280737591),
            ("Antonella", 0.003348367280737591),
            ("Vicente", 0.0033252938134605177),
            ("Valentina ", 0.0033252938134605177),
            ("Felicitas", 0.003319864762336501),
            ("Amparo", 0.0033035776089644494),
            ("Agostina", 0.003275075090563359),
            ("Thiago Ezequiel", 0.0032452153093812646),
            ("Ramiro", 0.0032139982654181658),
            ("Luciano", 0.0032072119515131445),
            ("Helena", 0.003143420600805943),
            ("Juliana", 0.0030728429361937194),
            ("Genaro", 0.0030375541038876078),
            ("Constanza", 0.0029832635926474362),
            ("Federico", 0.0029303303441882687),
            ("Lucia", 0.0029289730814072643),
            ("Paloma", 0.0029004705630061743),
            ("Luz Milagros", 0.00289911330022517),
            ("Thiago Agustin", 0.0028828261468531184),
            ("Lucio", 0.002866538993481067),
            ("Joaquín", 0.0028651817307000626),
            ("Lisandro", 0.0028651817307000626),
            ("Ian Benjamin", 0.002852966365671024),
            ("Sebastian", 0.0028448227889849983),
            ("Maria Paz", 0.002839393737860981),
            ("Abril", 0.0028244638472699336),
            ("Tiziano Benjamin", 0.002808176693897882),
            ("Thiago Nicolas", 0.002778316912715788),
            ("Maria Emilia", 0.002756600708219719),
            ("Thiago Valentin", 0.0027443853431906805),
            ("Lautaro Ezequiel", 0.0027023101969795476),
            ("Gonzalo", 0.002681951255264483),
            ("Zoe", 0.002681951255264483),
            ("Matilda", 0.002680593992483479),
            ("Alejo", 0.0026710931530164487),
            ("Paulina", 0.0026371615834913415),
            ("Bautista Benjamin", 0.0025543685538500795),
            ("Camilo", 0.002532652349354011),
            ("Milo", 0.0025136506704199505),
            ("Thiago Nahuel", 0.0024892199403618734),
            ("Octavio", 0.002487862677580869),
            ("Santiago Benjamin", 0.002441715743026723),
            ("Fausto", 0.0023928542829105685),
            ("Sofia Belen", 0.002292416837116251),
            ("Martín", 0.002288345048773238),
            ("Maia", 0.002282915997649221),
            ("Francesco", 0.002280201472087212),
            ("Sol", 0.0022761296837441993),
            ("Geronimo", 0.002273415158182191),
            ("Violeta", 0.0022679861070581736),
            ("Lourdes", 0.002259842530372148),
            ("Juan", 0.0022584852675911434),
            ("Benjamin Ezequiel", 0.002251698953686122),
            ("Luciana", 0.002242198114219092),
            ("Micaela", 0.002232697274752062),
            ("Alma Valentina", 0.002223196435285032),
            ("Nina", 0.002204194756350972),
            ("Zoe Valentina", 0.002200122968007959),
            ("Augusto", 0.0021892648657599245),
            ("Sara", 0.0021675486612638558),
            ("Tobias", 0.0021512615078918044),
            ("Gael", 0.002145832456767787),
            ("Antonia", 0.002139046142862766),
            ("Ezequiel", 0.0020956137338706284),
            ("Milena", 0.0020820411060605854),
            ("Ludmila", 0.002079326580498577),
            ("Zoe Jazmin", 0.002052181324878491),
            ("Miguel Angel", 0.0020467522737544737),
            ("Luana", 0.0020263933320394095),
            ("Giuliana", 0.0019938190252953066),
            ("Maite", 0.0019897472369522938),
            ("Valentina Jazmin", 0.0019761746091422508),
            ("Juan Gabriel", 0.0019748173463612463),
            ("Thiago Leonel", 0.001968031032456225),
            ("Juan Francisco", 0.001962601981332208),
            ("Mia Jazmin", 0.001950386616303169),
            ("Elena", 0.0019463148279601562),
            ("Ian", 0.0019259558862450919),
            ("Jazmín", 0.0019232413606830832),
            ("Alma Mia", 0.0019164550467780617),
            ("Candela", 0.0019110259956540447),
            ("Morena Jazmin", 0.001898810630625006),
            ("Santino Ezequiel", 0.001885238002814963),
            ("Mateo Ezequiel", 0.0018838807400339587),
            ("Joaquina", 0.0018703081122239157),
            ("Tiziana", 0.0018662363238809028),
            ("Luna", 0.00186216453553789),
            ("Francisca", 0.001856735484413873),
            ("Thiago Lionel", 0.001856735484413873),
            ("Enzo", 0.0018513064332898556),
            ("Emiliano", 0.0018458773821658386),
            ("Justina", 0.0018350192799178042),
            ("Lautaro Nicolas", 0.001829590228793787),
            ("Antonio", 0.0018105885498597269),
            ("Angelina", 0.0018092312870787227),
            ("Tomas Benjamin", 0.0017915868709256668),
            ("Mateo Agustin", 0.0017848005570206453),
            ("Uma", 0.0017766569803346196),
            ("Luciano Benjamin", 0.0017657988780865853),
            ("Kiara", 0.0017562980386195551),
            ("Alfonsina", 0.0017467971991525252),
            ("Tiziano Valentin", 0.0017400108852475037),
            ("Santino Nicolas", 0.0017345818341234864),
            ("Alvaro", 0.0017305100457804736),
            ("Florencia", 0.0017250809946564565),
            ("Mia Morena", 0.0017169374179704306),
            ("Luz Maria", 0.0017155801551894264),
            ("Nahuel", 0.001704722052941392),
            ("Thiago Emanuel", 0.0017020075273793834),
            ("Ian Ezequiel", 0.0016965784762553663),
            ("Uma ", 0.0016870776367883362),
            ("Santino Gabriel", 0.001685720374007332),
            ("Bastian", 0.0016843631112263275),
            ("Alma Jazmin", 0.0016762195345403018),
            ("Mateo Valentin", 0.0016680759578542761),
            ("Thiago Gabriel", 0.001666718695073272),
            ("Carmela", 0.0016653614322922675),
            ("Gabriel", 0.001649074278920216),
            ("Dylan", 0.0016409307022341904),
            ("Alexander", 0.0016341443883291689),
            ("Isabel", 0.0016287153372051516),
            ("Paula", 0.0016246435488621387),
            ("Lucas Benjamin", 0.0016232862860811345),
            ("Santiago Nicolas", 0.0016069991327090829),
            ("Maria Luz", 0.0016069991327090829),
            ("Luisana", 0.0016042846071470742),
            ("Nahiara Jazmin", 0.001597498293242053),
            ("Thiago Joaquin", 0.00158392566543201),
            ("Juan David", 0.0015784966143079927),
            ("Rosario", 0.001570353037621967),
            ("Abigail", 0.001556780409811924),
            ("Santino Agustin", 0.0015540658842499156),
            ("Maria Guadalupe", 0.001547279570344894),
            ("Oriana", 0.001547279570344894),
            ("Benjamin Alejandro", 0.001547279570344894),
            ("Ivan", 0.0015255633658488254),
            ("Maximiliano", 0.0015255633658488254),
            ("Gino", 0.0015187770519438039),
            ("Rafael", 0.0015092762124767737),
            ("Thiago Daniel", 0.0015065616869147653),
            ("Luisina", 0.0014984181102287394),
            ("Ciro Benjamin", 0.001495703584666731),
            ("Lucía ", 0.0014943463218857265),
            ("Nahiara", 0.0014712728546086536),
            ("León", 0.001468558329046645),
            ("Faustino", 0.001468558329046645),
            ("Emanuel", 0.001468558329046645),
            ("Renzo", 0.0014468421245505763),
            ("Martin", 0.0014441275989885679),
            ("Candelaria", 0.0014427703362075634),
            ("Maria Belen", 0.0014278404456165162),
            ("Ema", 0.0014156250805874777),
            ("Giovanni", 0.001412910555025469),
            ("Lautaro Gabriel", 0.0014047669783394433),
            ("Lautaro Nahuel", 0.0013966234016534176),
            ("Dylan Benjamin", 0.001393908876091409),
            ("Lucas Ezequiel", 0.0013871225621863875),
            ("Charo", 0.0013816935110623704),
            ("Constantino", 0.0013816935110623704),
            ("Juan Sebastian", 0.001374907197157349),
            ("Dylan Ezequiel", 0.001374907197157349),
            ("Samuel", 0.0013735499343763445),
            ("Priscila", 0.0013694781460333316),
            ("Jeronimo", 0.0013681208832523274),
            ("Máximo", 0.0013654063576903188),
            ("Mateo Joaquin", 0.0013586200437852973),
            ("Angel Gabriel", 0.0013559055182232886),
        ]
    )

    """
        Top 100 most common surnames in Argentina.
        Source: https://apellidos.de/argentina
        Weighted according to their most common ocurrance.
    """
    last_names = OrderedDict(
        [
            ("Gonzalez", 0.04854693696239391),
            ("Rodriguez", 0.0405541623163279),
            ("Gomez", 0.03682803729477576),
            ("Fernandez", 0.034483269554972315),
            ("Lopez", 0.03332903288377172),
            ("Diaz", 0.029734390864767524),
            ("Martinez", 0.02856301725128834),
            ("Perez", 0.024756520718787717),
            ("Garcia", 0.023949363254085327),
            ("Sanchez", 0.02305996343320753),
            ("Romero", 0.0221616583890496),
            ("Sosa", 0.01620443818368168),
            ("Torres", 0.014441203974214065),
            ("Alvarez", 0.014438584790896377),
            ("Ruiz", 0.014034781557117952),
            ("Ramirez", 0.01340602789325466),
            ("Flores", 0.012397043645472136),
            ("Benitez", 0.012046821418992701),
            ("Acosta", 0.011839007931186422),
            ("Medina", 0.01163845332286059),
            ("Herrera", 0.011188178293645473),
            ("Suarez", 0.011025040589858042),
            ("Aguirre", 0.010925586457594973),
            ("Gimenez", 0.010804056351654247),
            ("Gutierrez", 0.010736780757294201),
            ("Pereyra", 0.010597290537175041),
            ("Rojas", 0.010483168978332917),
            ("Molina", 0.01020605938332152),
            ("Castro", 0.00982904665319259),
            ("Ortiz", 0.009338660702312304),
            ("Silva", 0.00916369925669074),
            ("Nuñez", 0.008963294315983064),
            ("Luna", 0.00893530647138834),
            ("Juarez", 0.008729588330236212),
            ("Cabrera", 0.008523720521465928),
            ("Rios", 0.008519903997203012),
            ("Morales", 0.008325410927412402),
            ("Godoy", 0.008276918619130635),
            ("Moreno", 0.008114903422479358),
            ("Ferreyra", 0.008084969898848637),
            ("Dominguez", 0.008067383953715588),
            ("Carrizo", 0.007802771604820016),
            ("Peralta", 0.007790424026322343),
            ("Castillo", 0.007746346912776107),
            ("Ledesma", 0.007626537984444146),
            ("Quiroga", 0.007620027443054464),
            ("Vega", 0.007410866946684802),
            ("Vera", 0.007169303410984885),
            ("Muñoz", 0.006990675108718558),
            ("Ojeda", 0.006979075868311654),
            ("Ponce", 0.006857321260943696),
            ("Villalba", 0.006694632560010729),
            ("Cardozo", 0.0066311734899136),
            ("Navarro", 0.006361322774382651),
            ("Coronel", 0.006334158101687771),
            ("Vazquez", 0.006315225147991341),
            ("Ramos", 0.006305122583765973),
            ("Vargas", 0.006255058765493592),
            ("Caceres", 0.006177755440717255),
            ("Arias", 0.006152461613249296),
            ("Figueroa", 0.006118038061073967),
            ("Cordoba", 0.005941954108316251),
            ("Correa", 0.00593237538075442),
            ("Maldonado", 0.005926388676028276),
            ("Paz", 0.005885679083890496),
            ("Rivero", 0.005804409567233089),
            ("Miranda", 0.005788769301136037),
            ("Mansilla", 0.005749556385179793),
            ("Farias", 0.005590833876127895),
            ("Roldan", 0.005557981833943179),
            ("Mendez", 0.005512632545642637),
            ("Guzman", 0.005438771576083833),
            ("Aguero", 0.005383170055939769),
            ("Hernandez", 0.005325323521523401),
            ("Lucero", 0.005317316303952183),
            ("Cruz", 0.0052402374806030765),
            ("Paez", 0.005229985248759555),
            ("Escobar", 0.005203494080346367),
            ("Mendoza", 0.005196085533247763),
            ("Barrios", 0.005190098828521619),
            ("Bustos", 0.005104264449510527),
            ("Avila", 0.005051132445065998),
            ("Ayala", 0.004944419433322477),
            ("Blanco", 0.004939405568114332),
            ("Soria", 0.004921220952508669),
            ("Maidana", 0.004808296734611774),
            ("Acuña", 0.0048020855284584),
            ("Leiva", 0.004789363780915343),
            ("Duarte", 0.004590380682580126),
            ("Moyano", 0.004529840131036993),
            ("Campos", 0.004521832913465775),
            ("Soto", 0.0044916000545987475),
            ("Martin", 0.004448271279143279),
            ("Valdez", 0.004368049435812946),
            ("Bravo", 0.004366403092013257),
            ("Chavez", 0.004326142502729937),
            ("Velazquez", 0.004323448485603173),
            ("Olivera", 0.004298079824326137),
            ("Toledo", 0.00428865076438246),
            ("Franco", 0.004282065389183701),
        ]
    )

    prefixes = ("Sr(a).", "Dr(a).")
