@echo off
setlocal

echo ========================================
echo     Crypto Trader Frontend Startup
echo ========================================

echo [INFO] Current directory: %CD%

echo [CHECK] Checking Node.js environment...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo [TIP] Please install Node.js 16+
    pause
    exit /b 1
)

node --version
npm --version
echo [SUCCESS] Node.js environment check passed

echo [INFO] Skipping backend connection check for now...
echo [TIP] Make sure backend is running at http://localhost:8000

if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd frontend
echo [INFO] Switched to frontend directory: %CD%

if not exist "package.json" (
    echo [ERROR] package.json file not found
    pause
    exit /b 1
)
echo [SUCCESS] package.json file check passed

echo [CHECK] Checking frontend dependencies...
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    echo [INFO] This may take a few minutes, please wait...
    npm install
    if errorlevel 1 (
        echo [ERROR] Frontend dependencies installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Frontend dependencies installation completed
) else (
    echo [SUCCESS] Frontend dependencies already installed
)

echo [CHECK] Checking key files...
if not exist "src\App.tsx" (
    echo [ERROR] Missing key files
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

echo ========================================
echo     Starting Frontend Development Server
echo ========================================

echo [INFO] Starting Vite development server...
echo [INFO] Frontend URL: http://localhost:5173
echo [INFO] Backend API: http://localhost:8000

echo [SUCCESS] Frontend development server starting!
echo [TIP] Browser will automatically open trading interface

echo ========================================
echo Press Ctrl+C to stop development server
echo ========================================

npm run dev

echo [INFO] Frontend development server stopped
pause
