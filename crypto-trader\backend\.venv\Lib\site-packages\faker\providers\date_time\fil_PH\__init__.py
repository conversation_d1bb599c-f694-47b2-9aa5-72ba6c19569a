from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    """Provider for datetimes for fil_PH locale"""

    DAY_NAMES = {
        "0": "Ling<PERSON>",
        "1": "<PERSON><PERSON>",
        "2": "<PERSON><PERSON>",
        "3": "<PERSON><PERSON><PERSON><PERSON>",
        "4": "<PERSON><PERSON><PERSON>",
        "5": "<PERSON><PERSON><PERSON><PERSON>",
        "6": "Sabad<PERSON>",
    }
    MONTH_NAMES = {
        "01": "Enero",
        "02": "Pebrero",
        "03": "Marso",
        "04": "<PERSON>bril",
        "05": "Mayo",
        "06": "<PERSON>nyo",
        "07": "<PERSON>ly<PERSON>",
        "08": "Agosto",
        "09": "Setyembre",
        "10": "Oktubre",
        "11": "<PERSON>by<PERSON><PERSON>",
        "12": "Di<PERSON>em<PERSON>",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
