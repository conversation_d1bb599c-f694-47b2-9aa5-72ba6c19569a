from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    def day_of_week(self) -> str:
        day = self.date("%w")
        DAY_NAMES = {
            "0": "<PERSON><PERSON>",
            "1": "<PERSON><PERSON><PERSON>",
            "2": "<PERSON><PERSON>",
            "3": "<PERSON><PERSON>",
            "4": "<PERSON><PERSON>",
            "5": "Sabtu",
            "6": "<PERSON><PERSON>",
        }

        return DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        MONTH_NAMES = {
            "01": "<PERSON><PERSON><PERSON>",
            "02": "<PERSON><PERSON><PERSON>",
            "03": "<PERSON><PERSON>",
            "04": "April",
            "05": "<PERSON>",
            "06": "Juni",
            "07": "Juli",
            "08": "<PERSON>gus<PERSON>",
            "09": "September",
            "10": "Oktober",
            "11": "November",
            "12": "Desember",
        }

        return MONTH_NAMES[month]
