@echo off

echo Starting frontend test script...
echo Current directory: %CD%

echo.
echo Checking if we are in the right directory...
if exist "frontend" (
    echo SUCCESS: Frontend directory found
) else (
    echo ERROR: Frontend directory not found
    echo Current directory contents:
    dir /b
    echo.
    echo Please make sure you are running this from crypto-trader directory
    pause
    exit /b 1
)

echo.
echo Checking Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo SUCCESS: Node.js found
    node --version
    npm --version
)

echo.
echo Switching to frontend directory...
cd frontend
echo Current directory: %CD%

echo.
echo Checking package.json...
if exist "package.json" (
    echo SUCCESS: package.json found
) else (
    echo ERROR: package.json not found
    echo Current directory contents:
    dir /b
    pause
    exit /b 1
)

echo.
echo Checking node_modules...
if exist "node_modules" (
    echo SUCCESS: node_modules found
) else (
    echo INFO: node_modules not found, need to install dependencies
    echo Running npm install...
    npm install
    if errorlevel 1 (
        echo ERROR: npm install failed
        pause
        exit /b 1
    )
    echo SUCCESS: Dependencies installed
)

echo.
echo All checks passed!
echo You can now run: npm run dev
echo.
echo Do you want to start the development server now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Starting development server...
    npm run dev
) else (
    echo Skipping development server start
)

echo.
echo Script completed
pause
