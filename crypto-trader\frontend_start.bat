@echo off
setlocal

echo.
echo ========================================
echo     Crypto Trader Frontend Startup
echo ========================================
echo.

cd /d "%~dp0"
echo [INFO] Current directory: %CD%

echo.
echo [CHECK] Checking Node.js environment...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo [TIP] Please install Node.js 16+
    pause
    exit /b 1
)

node --version
npm --version
echo [SUCCESS] Node.js environment check passed

echo.
echo [CHECK] Checking backend service connection...
echo [INFO] Trying to connect to backend server http://localhost:8000/health

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [WARNING] Cannot connect to backend server
    echo [TIP] Please ensure backend server is running
    echo [TIP] Run backend_start.bat to start backend
    echo [TIP] Backend address: http://localhost:8000
    echo.
    echo [CHOICE] Continue starting frontend?
    echo [TIP] Frontend features limited without backend
    echo          Press any key to continue, or Ctrl+C to cancel
    pause >nul
    echo.
) else (
    echo [SUCCESS] Successfully connected to backend server!
    echo [INFO] Backend service running normally, all features available
    echo.
)

if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd frontend
echo [INFO] Switched to frontend directory: %CD%

if not exist "package.json" (
    echo [ERROR] package.json file not found
    pause
    exit /b 1
)
echo [SUCCESS] package.json file check passed

echo.
echo [CHECK] Checking frontend dependencies...
if not exist "node_modules" (
    echo [INFO] First run, installing frontend dependencies...
    echo [INFO] This may take a few minutes, please wait...
    npm install
    if errorlevel 1 (
        echo [ERROR] Frontend dependencies installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Frontend dependencies installation completed
) else (
    echo [SUCCESS] Frontend dependencies already installed
)

echo.
echo [CHECK] Checking key files...
if not exist "src\App.tsx" (
    echo [ERROR] Missing key files
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

echo.
echo ========================================
echo     Starting Frontend Development Server
echo ========================================
echo.
echo [INFO] Starting Vite development server...
echo [INFO] Frontend URL: http://localhost:5173
echo [INFO] Backend API: http://localhost:8000
echo.
echo [SUCCESS] Frontend development server starting!
echo [TIP] Browser will automatically open trading interface
echo.
echo ========================================
echo Press Ctrl+C to stop development server
echo ========================================
echo.

npm run dev

echo.
echo [INFO] Frontend development server stopped
pause
