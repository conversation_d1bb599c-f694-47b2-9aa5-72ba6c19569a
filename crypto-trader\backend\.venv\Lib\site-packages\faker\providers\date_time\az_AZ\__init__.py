from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "<PERSON><PERSON>",
        "1": "<PERSON><PERSON> ertəsi",
        "2": "<PERSON>ə<PERSON><PERSON>ənbə axşamı",
        "3": "<PERSON><PERSON><PERSON><PERSON><PERSON>nbə",
        "4": "<PERSON>ü<PERSON>ə axşamı",
        "5": "<PERSON>ü<PERSON><PERSON>",
        "6": "Şənbə",
    }

    MONTH_NAMES = {
        "01": "Yanvar",
        "02": "<PERSON><PERSON><PERSON>",
        "03": "Mart",
        "04": "Aprel",
        "05": "May",
        "06": "<PERSON>yun",
        "07": "<PERSON>yu<PERSON>",
        "08": "Avqust",
        "09": "<PERSON><PERSON>abr",
        "10": "<PERSON><PERSON>ab<PERSON>",
        "11": "<PERSON>yabr",
        "12": "<PERSON><PERSON>br",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
