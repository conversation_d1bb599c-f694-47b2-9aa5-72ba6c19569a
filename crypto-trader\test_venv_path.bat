@echo off
echo Testing virtual environment path detection...
echo Current directory: %CD%
echo.

echo Checking ..\\.venv
if exist "..\\.venv" (
    echo [SUCCESS] Found ..\\.venv
    dir "..\\.venv" /b
) else (
    echo [NOT FOUND] ..\\.venv not found
)

echo.
echo Checking .venv
if exist ".venv" (
    echo [SUCCESS] Found .venv
    dir ".venv" /b
) else (
    echo [NOT FOUND] .venv not found
)

echo.
echo Checking backend\\venv
if exist "backend\\venv" (
    echo [SUCCESS] Found backend\\venv
    dir "backend\\venv" /b
) else (
    echo [NOT FOUND] backend\\venv not found
)

echo.
echo Full path check:
echo Parent directory: %CD%\\..
if exist "%CD%\\..\\.venv" (
    echo [SUCCESS] Full path .venv exists
) else (
    echo [NOT FOUND] Full path .venv not found
)

pause
