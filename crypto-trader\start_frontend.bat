@echo off

echo ========================================
echo     Crypto Trader Frontend Startup
echo ========================================

cd /d "%~dp0"

where node >nul 2>&1 || (
    echo [ERROR] Node.js not found, please install from https://nodejs.org/
    pause
    exit /b 1
)

node --version

if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    pause
    exit /b 1
)

cd frontend

if not exist "node_modules" (
    echo [INFO] First run, installing dependencies...
    npm install || (
        echo [ERROR] Dependencies installation failed
        pause
        exit /b 1
    )
)

echo [INFO] Starting frontend development server http://localhost:5173
npm run dev

echo.
echo [INFO] Frontend service stopped
pause
