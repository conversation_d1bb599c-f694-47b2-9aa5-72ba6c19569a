@echo off
setlocal enabledelayedexpansion

echo.
echo ================================================
echo           Frontend Service Startup
echo           Crypto Trader Frontend
echo ================================================
echo.

cd /d "%~dp0"
echo [INFO] Current directory: %CD%

echo.
echo [CHECK] Checking Node.js environment...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo [TIP] Please install Node.js 16+
    echo.
    pause
    exit /b 1
)

node --version
npm --version
echo [SUCCESS] Node.js environment check passed

echo.
echo [CHECK] Checking backend service connection status...
echo [INFO] Trying to connect to backend server http://localhost:8000/health

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [WARNING] Cannot connect to backend server
    echo [WARNING] 无法连接到后端服务器
    echo [TIP] Please ensure backend server is running
    echo [TIP] 请确保后端服务器已启动
    echo [TIP] Run start_backend.bat to start backend
    echo [TIP] 运行 start_backend.bat 启动后端
    echo [TIP] Backend address should be: http://localhost:8000
    echo.
    echo [CHOICE] Continue starting frontend?
    echo [CHOICE] 是否继续启动前端？
    echo [TIP] Frontend features limited without backend
    echo [TIP] 后端未启动时前端功能受限
    echo          Press any key to continue, or Ctrl+C to cancel
    echo          按任意键继续，或按Ctrl+C取消
    pause >nul
    echo.
) else (
    echo [SUCCESS] Successfully connected to backend server!
    echo [SUCCESS] 成功连接后端服务器！
    echo [INFO] Backend service running normally, all features available
    echo [INFO] 后端服务运行正常，所有功能可用
    echo.
)

REM ---------- Enter Frontend Directory ----------
if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd frontend
echo [INFO] Switched to frontend directory: %CD%

REM ---------- Check package.json ----------
if not exist "package.json" (
    echo [ERROR] package.json file not found
    pause
    exit /b 1
)
echo [SUCCESS] package.json file check passed

REM ---------- Check/Install Dependencies ----------
echo.
echo [CHECK] Checking frontend dependencies...
if not exist "node_modules" (
    echo [INFO] First run, installing frontend dependencies...
    echo [INFO] This may take a few minutes, please wait...
    npm install
    if errorlevel 1 (
        echo [ERROR] Frontend dependencies installation failed
        echo [TIP] Please check network connection and package.json file
        pause
        exit /b 1
    )
    echo [SUCCESS] Frontend dependencies installation completed
) else (
    echo [SUCCESS] Frontend dependencies already installed, skipping installation
)

REM ---------- Check Key Files ----------
echo.
echo [CHECK] Checking key files...
set "missing_files="
if not exist "src\App.tsx" set "missing_files=%missing_files% src\App.tsx"
if not exist "src\main.tsx" set "missing_files=%missing_files% src\main.tsx"
if not exist "index.html" set "missing_files=%missing_files% index.html"

if not "%missing_files%"=="" (
    echo [ERROR] Missing key files:%missing_files%
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

echo.
echo ================================================
echo    Frontend Server Starting - 前端服务器启动中
echo ================================================
echo.
echo [INFO] Starting Vite development server...
echo [INFO] 正在启动Vite开发服务器...
echo [INFO] Frontend URL: http://localhost:5173
echo [INFO] 前端地址: http://localhost:5173
echo [INFO] Backend API: http://localhost:8000
echo [INFO] 后端API: http://localhost:8000
echo.
echo [SUCCESS] Frontend development server starting!
echo [SUCCESS] 前端开发服务器启动中！
echo [TIP] Browser will automatically open trading interface
echo [TIP] 浏览器将自动打开交易界面
echo.
echo ----------------------------------------
echo Press Ctrl+C to stop development server
echo 按 Ctrl+C 停止开发服务器
echo ----------------------------------------
echo.

npm run dev

echo.
echo [INFO] Frontend development server stopped
echo [INFO] 前端开发服务器已停止
pause
