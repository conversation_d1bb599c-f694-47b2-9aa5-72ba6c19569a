@echo off
setlocal

echo.
echo ========================================
echo     Crypto Trader Frontend Startup
echo     加密货币交易系统前端启动
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"
echo [INFO] 当前目录 Current directory: %CD%

REM 检查Node.js环境
echo.
echo [检查] 正在检查Node.js环境...
where node >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Node.js环境
    echo [提示] 请安装Node.js 16+
    pause
    exit /b 1
)

node --version
npm --version
echo [成功] Node.js环境检查通过

REM 检查后端连接
echo.
echo [检查] 正在检查后端服务连接...
echo [信息] 尝试连接后端服务器 http://localhost:8000/health

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [警告] 无法连接到后端服务器
    echo [提示] 请确保后端服务器已启动
    echo [提示] 运行 start_backend.bat 启动后端
    echo [提示] 后端地址: http://localhost:8000
    echo.
    echo [选择] 是否继续启动前端？
    echo [提示] 后端未启动时前端功能受限
    echo          按任意键继续，或按Ctrl+C取消
    pause >nul
    echo.
) else (
    echo [成功] 成功连接后端服务器！
    echo [信息] 后端服务运行正常，所有功能可用
    echo.
)

REM 检查前端目录
if not exist "frontend" (
    echo [错误] 未找到frontend目录
    echo [提示] 请在crypto-trader根目录运行此脚本
    pause
    exit /b 1
)

REM 切换到前端目录
cd frontend
echo [信息] 已切换到前端目录: %CD%

REM 检查package.json
if not exist "package.json" (
    echo [错误] 未找到package.json文件
    pause
    exit /b 1
)
echo [成功] package.json文件检查通过

REM 检查依赖
echo.
echo [检查] 正在检查前端依赖...
if not exist "node_modules" (
    echo [信息] 首次运行，正在安装前端依赖...
    echo [信息] 这可能需要几分钟，请耐心等待...
    npm install
    if errorlevel 1 (
        echo [错误] 前端依赖安装失败
        pause
        exit /b 1
    )
    echo [成功] 前端依赖安装完成
) else (
    echo [成功] 前端依赖已安装
)

REM 检查关键文件
echo.
echo [检查] 正在检查关键文件...
if not exist "src\App.tsx" (
    echo [错误] 缺少关键文件
    pause
    exit /b 1
)
echo [成功] 关键文件检查通过

REM 启动前端服务器
echo.
echo ========================================
echo     启动前端开发服务器
echo     Starting Frontend Development Server
echo ========================================
echo.
echo [信息] 正在启动Vite开发服务器...
echo [信息] 前端地址: http://localhost:5173
echo [信息] 后端API: http://localhost:8000
echo.
echo [成功] 前端开发服务器启动中！
echo [提示] 浏览器将自动打开交易界面
echo.
echo ========================================
echo 按 Ctrl+C 停止开发服务器
echo Press Ctrl+C to stop development server
echo ========================================
echo.

npm run dev

echo.
echo [信息] 前端开发服务器已停止
pause
