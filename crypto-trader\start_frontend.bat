@echo off
chcp 65001 >nul

REM ========================================
REM    加密货币交易系统 - 前端服务启动脚本
REM    Crypto Trader Frontend Startup Script
REM ========================================

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎨 前端服务启动程序                        ║
echo ║                  Crypto Trader Frontend                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"
echo [信息] 当前工作目录: %CD%

REM ---------- 检查Node.js环境 ----------
echo.
echo [检查] 正在检查Node.js环境...
where node >nul 2>&1
if errorlevel 1 (
    echo [错误] ❌ 未检测到Node.js环境
    echo [提示] 请先安装Node.js 16+: https://nodejs.org/zh-cn/
    echo.
    pause
    exit /b 1
)

node --version
npm --version
echo [成功] ✅ Node.js环境检查通过

REM ---------- 检查后端服务连接 ----------
echo.
echo [检查] 正在检查后端服务连接状态...
echo [信息] 尝试连接后端服务器 http://localhost:8000/health

REM 使用PowerShell检查后端连接
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [警告] ⚠️  无法连接到后端服务器
    echo [提示] 请确保后端服务器已启动 ^(运行 start_backend.bat^)
    echo [提示] 后端地址应为: http://localhost:8000
    echo.
    echo [选择] 是否继续启动前端? ^(后端未启动时前端功能受限^)
    echo          按任意键继续，或按Ctrl+C取消
    pause >nul
    echo.
) else (
    echo [成功] ✅ 成功连接后端服务器！
    echo [信息] 后端服务运行正常，可以正常使用所有功能
    echo.
)

REM ---------- 进入前端目录 ----------
if not exist "frontend" (
    echo [错误] ❌ 未找到frontend目录
    echo [提示] 请确保在crypto-trader根目录运行此脚本
    pause
    exit /b 1
)

cd frontend
echo [信息] 切换到前端目录: %CD%

REM ---------- 检查package.json ----------
if not exist "package.json" (
    echo [错误] ❌ 未找到package.json文件
    pause
    exit /b 1
)
echo [成功] ✅ package.json文件检查通过

REM ---------- 检查/安装依赖 ----------
echo.
echo [检查] 正在检查前端依赖...
if not exist "node_modules" (
    echo [信息] 🔧 首次运行，正在安装前端依赖...
    echo [信息] 这可能需要几分钟时间，请耐心等待...
    npm install
    if errorlevel 1 (
        echo [错误] ❌ 前端依赖安装失败
        echo [提示] 请检查网络连接和package.json文件
        pause
        exit /b 1
    )
    echo [成功] ✅ 前端依赖安装完成
) else (
    echo [成功] ✅ 前端依赖已安装，跳过安装步骤
)

REM ---------- 检查关键文件 ----------
echo.
echo [检查] 正在检查关键文件...
set "missing_files="
if not exist "src\App.tsx" set "missing_files=%missing_files% src\App.tsx"
if not exist "src\main.tsx" set "missing_files=%missing_files% src\main.tsx"
if not exist "index.html" set "missing_files=%missing_files% index.html"

if not "%missing_files%"=="" (
    echo [错误] ❌ 缺少关键文件:%missing_files%
    pause
    exit /b 1
)
echo [成功] ✅ 关键文件检查通过

REM ---------- 启动前端开发服务器 ----------
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎯 启动前端开发服务器                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [信息] 正在启动Vite开发服务器...
echo [信息] 前端地址: http://localhost:5173
echo [信息] 后端API: http://localhost:8000
echo.
echo [提示] 🎉 前端开发服务器即将启动！
echo [提示] 浏览器将自动打开交易界面
echo.
echo ----------------------------------------
echo 按 Ctrl+C 停止开发服务器
echo ----------------------------------------
echo.

REM 启动Vite开发服务器
npm run dev

REM ---------- 服务停止处理 ----------
echo.
echo [信息] 🛑 前端开发服务器已停止
pause
