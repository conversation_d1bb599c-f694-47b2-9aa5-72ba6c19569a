@echo off
setlocal enabledelayedexpansion

REM ========================================
REM    Crypto Trader Frontend Startup Script
REM ========================================

echo.
echo ================================================
echo           Frontend Service Startup
echo           Crypto Trader Frontend
echo ================================================
echo.

REM Switch to script directory
cd /d "%~dp0"
echo [INFO] Current directory: %CD%

REM ---------- Check Node.js Environment ----------
echo.
echo [CHECK] Checking Node.js environment...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo [TIP] Please install Node.js 16+: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

node --version
npm --version
echo [SUCCESS] Node.js environment check passed

REM ---------- Check Backend Service Connection ----------
echo.
echo [CHECK] Checking backend service connection status...
echo [INFO] Trying to connect to backend server http://localhost:8000/health

REM Use PowerShell to check backend connection
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [警告] 无法连接到后端服务器 Cannot connect to backend server
    echo [提示] 请确保后端服务器已启动 Please ensure backend server is running
    echo [提示] 运行 start_backend.bat 启动后端 ^(run start_backend.bat^)
    echo [提示] 后端地址应为 Backend address should be: http://localhost:8000
    echo.
    echo [选择] 是否继续启动前端？Continue starting frontend?
    echo [提示] 后端未启动时前端功能受限 ^(Frontend features limited without backend^)
    echo          按任意键继续，或按Ctrl+C取消 Press any key to continue, or Ctrl+C to cancel
    pause >nul
    echo.
) else (
    echo [成功] 成功连接后端服务器！Successfully connected to backend server!
    echo [信息] 后端服务运行正常，所有功能可用 Backend service running normally, all features available
    echo.
)

REM ---------- Enter Frontend Directory ----------
if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd frontend
echo [INFO] Switched to frontend directory: %CD%

REM ---------- Check package.json ----------
if not exist "package.json" (
    echo [ERROR] package.json file not found
    pause
    exit /b 1
)
echo [SUCCESS] package.json file check passed

REM ---------- Check/Install Dependencies ----------
echo.
echo [CHECK] Checking frontend dependencies...
if not exist "node_modules" (
    echo [INFO] First run, installing frontend dependencies...
    echo [INFO] This may take a few minutes, please wait...
    npm install
    if errorlevel 1 (
        echo [ERROR] Frontend dependencies installation failed
        echo [TIP] Please check network connection and package.json file
        pause
        exit /b 1
    )
    echo [SUCCESS] Frontend dependencies installation completed
) else (
    echo [SUCCESS] Frontend dependencies already installed, skipping installation
)

REM ---------- Check Key Files ----------
echo.
echo [CHECK] Checking key files...
set "missing_files="
if not exist "src\App.tsx" set "missing_files=%missing_files% src\App.tsx"
if not exist "src\main.tsx" set "missing_files=%missing_files% src\main.tsx"
if not exist "index.html" set "missing_files=%missing_files% index.html"

if not "%missing_files%"=="" (
    echo [ERROR] Missing key files:%missing_files%
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

REM ---------- Start Frontend Development Server ----------
echo.
echo ================================================
echo        启动前端开发服务器 Starting Frontend Development Server
echo ================================================
echo.
echo [信息] 正在启动Vite开发服务器... Starting Vite development server...
echo [信息] 前端地址 Frontend URL: http://localhost:5173
echo [信息] 后端API Backend API: http://localhost:8000
echo.
echo [成功] 前端开发服务器启动中！Frontend development server starting!
echo [提示] 浏览器将自动打开交易界面 Browser will automatically open trading interface
echo.
echo ----------------------------------------
echo 按 Ctrl+C 停止开发服务器 Press Ctrl+C to stop development server
echo ----------------------------------------
echo.

REM Start Vite development server
npm run dev

REM ---------- Service Stop Handling ----------
echo.
echo [INFO] Frontend development server stopped
pause
