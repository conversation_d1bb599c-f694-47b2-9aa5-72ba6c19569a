@echo off
setlocal enabledelayedexpansion

REM ========================================
REM    Crypto Trader Frontend Startup Script
REM ========================================

echo.
echo ================================================
echo           Frontend Service Startup
echo           Crypto Trader Frontend
echo ================================================
echo.

REM Switch to script directory
cd /d "%~dp0"
echo [INFO] Current directory: %CD%

REM ---------- Check Node.js Environment ----------
echo.
echo [CHECK] Checking Node.js environment...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo [TIP] Please install Node.js 16+: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

node --version
npm --version
echo [SUCCESS] Node.js environment check passed

REM ---------- Check Backend Service Connection ----------
echo.
echo [CHECK] Checking backend service connection status...
echo [INFO] Trying to connect to backend server http://localhost:8000/health

REM Use PowerShell to check backend connection
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if errorlevel 1 (
    echo [WARNING] Cannot connect to backend server
    echo [TIP] Please ensure backend server is running ^(run start_backend.bat^)
    echo [TIP] Backend address should be: http://localhost:8000
    echo.
    echo [CHOICE] Continue starting frontend? ^(Frontend features limited without backend^)
    echo          Press any key to continue, or Ctrl+C to cancel
    pause >nul
    echo.
) else (
    echo [SUCCESS] Successfully connected to backend server!
    echo [INFO] Backend service running normally, all features available
    echo.
)

REM ---------- Enter Frontend Directory ----------
if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd frontend
echo [INFO] Switched to frontend directory: %CD%

REM ---------- Check package.json ----------
if not exist "package.json" (
    echo [ERROR] package.json file not found
    pause
    exit /b 1
)
echo [SUCCESS] package.json file check passed

REM ---------- Check/Install Dependencies ----------
echo.
echo [CHECK] Checking frontend dependencies...
if not exist "node_modules" (
    echo [INFO] First run, installing frontend dependencies...
    echo [INFO] This may take a few minutes, please wait...
    npm install
    if errorlevel 1 (
        echo [ERROR] Frontend dependencies installation failed
        echo [TIP] Please check network connection and package.json file
        pause
        exit /b 1
    )
    echo [SUCCESS] Frontend dependencies installation completed
) else (
    echo [SUCCESS] Frontend dependencies already installed, skipping installation
)

REM ---------- Check Key Files ----------
echo.
echo [CHECK] Checking key files...
set "missing_files="
if not exist "src\App.tsx" set "missing_files=%missing_files% src\App.tsx"
if not exist "src\main.tsx" set "missing_files=%missing_files% src\main.tsx"
if not exist "index.html" set "missing_files=%missing_files% index.html"

if not "%missing_files%"=="" (
    echo [ERROR] Missing key files:%missing_files%
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

REM ---------- Start Frontend Development Server ----------
echo.
echo ================================================
echo        Starting Frontend Development Server
echo ================================================
echo.
echo [INFO] Starting Vite development server...
echo [INFO] Frontend URL: http://localhost:5173
echo [INFO] Backend API: http://localhost:8000
echo.
echo [SUCCESS] Frontend development server starting!
echo [TIP] Browser will automatically open trading interface
echo.
echo ----------------------------------------
echo Press Ctrl+C to stop development server
echo ----------------------------------------
echo.

REM Start Vite development server
npm run dev

REM ---------- Service Stop Handling ----------
echo.
echo [INFO] Frontend development server stopped
pause
