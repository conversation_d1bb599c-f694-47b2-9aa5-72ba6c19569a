@echo off
setlocal enabledelayedexpansion

REM ========================================
REM    Crypto Trader Backend Startup Script
REM ========================================

echo.
echo ================================================
echo           Backend Service Startup
echo           Crypto Trader Backend
echo ================================================
echo.

REM Switch to script directory
cd /d "%~dp0"
echo [INFO] Current directory: %CD%

REM ---------- Check Python Environment ----------
echo.
echo [CHECK] Checking Python environment...
where python >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    echo [TIP] Please install Python 3.8+: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

python --version
echo [SUCCESS] Python environment check passed

REM ---------- Enter Backend Directory ----------
if not exist "backend" (
    echo [ERROR] Backend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

cd backend
echo [INFO] Switched to backend directory: %CD%

REM ---------- Check/Create Virtual Environment ----------
echo.
echo [CHECK] Checking virtual environment...
if not exist "venv" (
    echo [INFO] First run, creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] Virtual environment creation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Virtual environment created successfully
) else (
    echo [SUCCESS] Virtual environment already exists
)

REM ---------- Activate Virtual Environment ----------
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [ERROR] Virtual environment activation failed
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment activated

REM ---------- Check Dependencies Installation ----------
echo.
echo [CHECK] Checking dependencies installation status...
python -c "import fastapi, uvicorn, sqlalchemy, pydantic" 2>nul
if errorlevel 1 (
    echo [INFO] Dependencies not fully installed, installing packages...
    echo [INFO] This may take a few minutes, please wait...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        echo [TIP] Please check network connection and requirements.txt file
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installation completed
) else (
    echo [SUCCESS] Dependencies already installed, skipping installation
)

REM ---------- Check Key Files ----------
echo.
echo [CHECK] Checking key files...
if not exist "app\main.py" (
    echo [ERROR] Main program file app\main.py not found
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

REM ---------- Start Backend Server ----------
echo.
echo ================================================
echo           Starting Backend Server
echo ================================================
echo.
echo [INFO] Starting FastAPI server...
echo [INFO] Server URL: http://localhost:8000
echo [INFO] API Docs: http://localhost:8000/docs
echo [INFO] Health Check: http://localhost:8000/health
echo.
echo [SUCCESS] Backend server startup successful!
echo [TIP] Now you can start the frontend service
echo [TIP] Please run start_frontend.bat to start frontend interface
echo.
echo ----------------------------------------
echo Press Ctrl+C to stop server
echo ----------------------------------------
echo.

REM Start uvicorn server
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

REM ---------- Service Stop Handling ----------
echo.
echo [INFO] Backend server stopped
pause
