@echo off
setlocal

echo.
echo ========================================
echo     Crypto Trader Backend Startup
echo     加密货币交易系统后端启动
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"
echo [INFO] 当前目录 Current directory: %CD%

REM 检查Python环境
echo.
echo [检查] 正在检查Python环境...
where python >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python环境
    echo [提示] 请安装Python 3.8+
    pause
    exit /b 1
)

python --version
echo [成功] Python环境检查通过

REM 检查后端目录
if not exist "backend" (
    echo [错误] 未找到backend目录
    echo [提示] 请在crypto-trader根目录运行此脚本
    pause
    exit /b 1
)

REM 检查虚拟环境
echo.
echo [检查] 正在检查虚拟环境...
if exist "..\.venv" (
    echo [成功] 找到虚拟环境
    echo [信息] 正在激活虚拟环境...
    call "..\.venv\Scripts\activate.bat"
    if errorlevel 1 (
        echo [错误] 虚拟环境激活失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境已激活
) else (
    echo [错误] 未找到虚拟环境
    echo [提示] 请确保虚拟环境位于上级目录的.venv文件夹中
    pause
    exit /b 1
)

REM 切换到后端目录
cd backend
echo [信息] 已切换到后端目录: %CD%

REM 检查依赖
echo.
echo [检查] 正在检查依赖安装状态...
python -c "import fastapi, uvicorn" 2>nul
if errorlevel 1 (
    echo [信息] 依赖未完全安装，正在安装...
    echo [信息] 这可能需要几分钟，请耐心等待...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
    echo [成功] 依赖安装完成
) else (
    echo [成功] 依赖已安装
)

REM 检查关键文件
echo.
echo [检查] 正在检查关键文件...
if not exist "app\main.py" (
    echo [错误] 未找到主程序文件 app\main.py
    pause
    exit /b 1
)
echo [成功] 关键文件检查通过

REM 启动服务器
echo.
echo ========================================
echo     启动后端服务器
echo     Starting Backend Server
echo ========================================
echo.
echo [信息] 正在启动FastAPI服务器...
echo [信息] 服务地址: http://localhost:8000
echo [信息] API文档: http://localhost:8000/docs
echo [信息] 健康检查: http://localhost:8000/health
echo.
echo [成功] 后端服务器启动成功！
echo [提示] 现在可以启动前端服务了！
echo [提示] 请运行 start_frontend.bat 启动前端
echo.
echo ========================================
echo 按 Ctrl+C 停止服务器
echo Press Ctrl+C to stop server
echo ========================================
echo.
echo [重要] 看到 "Application startup complete" 后
echo [重要] 就可以启动前端了！
echo.

REM 设置Python路径并启动服务器
set PYTHONPATH=%CD%
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

echo.
echo [信息] 后端服务器已停止
pause
