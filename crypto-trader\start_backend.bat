@echo off
chcp 65001 >nul

REM ========================================
REM    加密货币交易系统 - 后端服务启动脚本
REM    Crypto Trader Backend Startup Script
REM ========================================

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 后端服务启动程序                        ║
echo ║                  Crypto Trader Backend                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"
echo [信息] 当前工作目录: %CD%

REM ---------- 检查Python环境 ----------
echo.
echo [检查] 正在检查Python环境...
where python >nul 2>&1
if errorlevel 1 (
    echo [错误] ❌ 未检测到Python环境
    echo [提示] 请先安装Python 3.8+: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

python --version
echo [成功] ✅ Python环境检查通过

REM ---------- 进入后端目录 ----------
if not exist "backend" (
    echo [错误] ❌ 未找到backend目录
    echo [提示] 请确保在crypto-trader根目录运行此脚本
    pause
    exit /b 1
)

cd backend
echo [信息] 切换到后端目录: %CD%

REM ---------- 检查/创建虚拟环境 ----------
echo.
echo [检查] 正在检查虚拟环境...
if not exist "venv" (
    echo [信息] 🔧 首次运行，正在创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo [错误] ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo [成功] ✅ 虚拟环境创建成功
) else (
    echo [成功] ✅ 虚拟环境已存在
)

REM ---------- 激活虚拟环境 ----------
echo [信息] 正在激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [错误] ❌ 虚拟环境激活失败
    pause
    exit /b 1
)
echo [成功] ✅ 虚拟环境已激活

REM ---------- 检查依赖安装状态 ----------
echo.
echo [检查] 正在检查依赖安装状态...
python -c "import fastapi, uvicorn, sqlalchemy, pydantic" 2>nul
if errorlevel 1 (
    echo [信息] 🔧 依赖未完全安装，正在安装依赖包...
    echo [信息] 这可能需要几分钟时间，请耐心等待...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [错误] ❌ 依赖安装失败
        echo [提示] 请检查网络连接和requirements.txt文件
        pause
        exit /b 1
    )
    echo [成功] ✅ 依赖安装完成
) else (
    echo [成功] ✅ 依赖已安装，跳过安装步骤
)

REM ---------- 检查关键文件 ----------
echo.
echo [检查] 正在检查关键文件...
if not exist "app\main.py" (
    echo [错误] ❌ 未找到主程序文件 app\main.py
    pause
    exit /b 1
)
echo [成功] ✅ 关键文件检查通过

REM ---------- 启动后端服务器 ----------
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎯 启动后端服务器                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [信息] 正在启动FastAPI服务器...
echo [信息] 服务地址: http://localhost:8000
echo [信息] API文档: http://localhost:8000/docs
echo [信息] 健康检查: http://localhost:8000/health
echo.
echo [提示] 🎉 后端服务器启动成功！现在可以启动前端服务了
echo [提示] 请运行 start_frontend.bat 启动前端界面
echo.
echo ----------------------------------------
echo 按 Ctrl+C 停止服务器
echo ----------------------------------------
echo.

REM 启动uvicorn服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

REM ---------- 服务停止处理 ----------
echo.
echo [信息] 🛑 后端服务器已停止
pause
