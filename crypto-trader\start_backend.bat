@echo off
setlocal enabledelayedexpansion

echo.
echo ================================================
echo           Backend Service Startup
echo           Crypto Trader Backend
echo ================================================
echo.

cd /d "%~dp0"
echo [INFO] Current directory: %CD%

echo.
echo [CHECK] Checking Python environment...
where python >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    echo [TIP] Please install Python 3.8+
    echo.
    pause
    exit /b 1
)

python --version
echo [SUCCESS] Python environment check passed

if not exist "backend" (
    echo [ERROR] Backend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

echo.
echo [CHECK] Checking virtual environment...

if exist "..\.venv" (
    echo [SUCCESS] Found virtual environment in parent directory
    echo [INFO] Activating virtual environment...
    call "..\.venv\Scripts\activate.bat"
    if errorlevel 1 (
        echo [ERROR] Virtual environment activation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Virtual environment activated
    goto venv_ok
)

if exist ".venv" (
    echo [SUCCESS] Found virtual environment in current directory
    echo [INFO] Activating virtual environment...
    call ".venv\Scripts\activate.bat"
    if errorlevel 1 (
        echo [ERROR] Virtual environment activation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Virtual environment activated
    goto venv_ok
)

if exist "backend\venv" (
    echo [SUCCESS] Found virtual environment in backend directory
    echo [INFO] Activating virtual environment...
    call "backend\venv\Scripts\activate.bat"
    if errorlevel 1 (
        echo [ERROR] Virtual environment activation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Virtual environment activated
    goto venv_ok
)

echo [INFO] No virtual environment found, creating new one...
cd backend
python -m venv venv
if errorlevel 1 (
    echo [ERROR] Virtual environment creation failed
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment created successfully
echo [INFO] Activating virtual environment...
call "venv\Scripts\activate.bat"
if errorlevel 1 (
    echo [ERROR] Virtual environment activation failed
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment activated
cd ..

:venv_ok

cd backend
echo [INFO] Switched to backend directory: %CD%

echo.
echo [CHECK] Checking dependencies installation status...
python -c "import fastapi, uvicorn, sqlalchemy, pydantic" 2>nul
if errorlevel 1 (
    echo [INFO] Dependencies not fully installed, installing packages...
    echo [INFO] This may take a few minutes, please wait...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installation completed
) else (
    echo [SUCCESS] Dependencies already installed, skipping installation
)

echo.
echo [CHECK] Checking key files...
if not exist "app\main.py" (
    echo [ERROR] Main program file app\main.py not found
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

echo.
echo ================================================
echo    Backend Server Starting - 后端服务器启动中
echo ================================================
echo.
echo [INFO] Starting FastAPI server...
echo [INFO] Server URL: http://localhost:8000
echo [INFO] API Docs: http://localhost:8000/docs
echo [INFO] Health Check: http://localhost:8000/health
echo.
echo [SUCCESS] Backend server startup successful!
echo [TIP] Now you can start the frontend service!
echo [TIP] 现在可以启动前端服务了！
echo [TIP] Please run start_frontend.bat
echo.
echo ----------------------------------------
echo Press Ctrl+C to stop server
echo ----------------------------------------
echo.
echo [IMPORTANT] When you see "Application startup complete"
echo [IMPORTANT] 看到 Application startup complete 消息后
echo [IMPORTANT] You can start the frontend!
echo [IMPORTANT] 就可以启动前端了！
echo.

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

echo.
echo [INFO] Backend server stopped
pause
