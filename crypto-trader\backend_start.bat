@echo off
setlocal

echo.
echo ========================================
echo     Crypto Trader Backend Startup
echo ========================================
echo.

cd /d "%~dp0"
echo [INFO] Current directory: %CD%

echo.
echo [CHECK] Checking Python environment...
where python >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    echo [TIP] Please install Python 3.8+
    pause
    exit /b 1
)

python --version
echo [SUCCESS] Python environment check passed

if not exist "backend" (
    echo [ERROR] Backend directory not found
    echo [TIP] Please run this script from crypto-trader root directory
    pause
    exit /b 1
)

echo.
echo [CHECK] Checking virtual environment...
if exist "..\.venv" (
    echo [SUCCESS] Found virtual environment
    echo [INFO] Activating virtual environment...
    call "..\.venv\Scripts\activate.bat"
    if errorlevel 1 (
        echo [ERROR] Virtual environment activation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Virtual environment activated
) else (
    echo [ERROR] Virtual environment not found
    echo [TIP] Please ensure virtual environment exists in parent directory
    pause
    exit /b 1
)

cd backend
echo [INFO] Switched to backend directory: %CD%

echo.
echo [CHECK] Checking dependencies...
python -c "import fastapi, uvicorn" 2>nul
if errorlevel 1 (
    echo [INFO] Dependencies not fully installed, installing...
    echo [INFO] This may take a few minutes, please wait...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installation completed
) else (
    echo [SUCCESS] Dependencies already installed
)

echo.
echo [CHECK] Checking key files...
if not exist "app\main.py" (
    echo [ERROR] Main program file app\main.py not found
    pause
    exit /b 1
)
echo [SUCCESS] Key files check passed

echo.
echo ========================================
echo     Starting Backend Server
echo ========================================
echo.
echo [INFO] Starting FastAPI server...
echo [INFO] Server URL: http://localhost:8000
echo [INFO] API Docs: http://localhost:8000/docs
echo [INFO] Health Check: http://localhost:8000/health
echo.
echo [SUCCESS] Backend server startup successful!
echo [TIP] Now you can start the frontend service!
echo [TIP] Please run frontend_start.bat to start frontend
echo.
echo ========================================
echo Press Ctrl+C to stop server
echo ========================================
echo.
echo [IMPORTANT] When you see "Application startup complete"
echo [IMPORTANT] You can start the frontend!
echo.

set PYTHONPATH=%CD%
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

echo.
echo [INFO] Backend server stopped
pause
