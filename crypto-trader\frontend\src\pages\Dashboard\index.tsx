import React, { useState, useEffect } from 'react';
import { Card, Table, Typography, Row, Col, Statistic, Button, Alert, message, Tag, Space, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, ApiOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { apiService, AccountSummary, SystemStatus, Position, MarketData, EquityData } from '../../services/api';
import { Signal, getCurrentSignals } from '../../api';
import { wsService } from '../../services/websocket';
import { useMarket } from '../../contexts/MarketContext';
import EquityCurveChart from '../../components/EquityCurveChart';
import dayjs from 'dayjs';

const { Title } = Typography;

interface DashboardMarketData {
  symbol: string;
  price: string;
  change: string;
  changePercent: number;
  volume: string;
  time: string;
}

interface DashboardPosition {
  symbol: string;
  amount: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  leverage: number;
  liqPrice: number;
  margin: number;
}

const Dashboard: React.FC = () => {
  // 使用MarketContext获取市场数据
  const { prices, isConnected, enabledSymbols } = useMarket();
  
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [marketData, setMarketData] = useState<DashboardMarketData[]>([]);
  const [positions, setPositions] = useState<DashboardPosition[]>([]);
  const [accountSummary, setAccountSummary] = useState<AccountSummary | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [currentSignals, setCurrentSignals] = useState<Signal[]>([]);
  const [latestTrade, setLatestTrade] = useState<any>(null);
  const [riskAlert, setRiskAlert] = useState<{ active: boolean; message: string }>({ active: false, message: '' });

  // 资金曲线数据
  const [equityData, setEquityData] = useState<any[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState('1W');

  // 错误边界处理
  const handleError = (error: any, context: string) => {
    console.error(`Error in ${context}:`, error);
    setError(`${context}加载失败: ${error.message || '未知错误'}`);
  };

  // 监听MarketContext的价格数据变化
  useEffect(() => {
    if (prices.length > 0) {
      setMarketData(transformMarketData(prices));
    } else if (enabledSymbols.length === 0) {
      // 如果没有启用的策略，清空市场数据
      setMarketData([]);
    }
  }, [prices, enabledSymbols]);

  // 初始化数据加载
  useEffect(() => {
    loadInitialData();
  }, []);

  // WebSocket订阅（仅订阅信号和交易，市场数据由MarketContext管理）
  useEffect(() => {
    // 订阅信号更新
    const unsubscribeSignals = wsService.subscribeToSignals((signal) => {
      console.log('Received signal:', signal);
      
      // 更新现有信号或添加新信号
      setCurrentSignals(prev => {
        const existingIndex = prev.findIndex(s => s.symbol === signal.symbol);
        const newSignal: Signal = {
          symbol: signal.symbol,
          signal: signal.signal,
          strength: signal.strength || 1.0,
          strategy_name: signal.strategy_name,
          updated_at: signal.updated_at || new Date().toISOString()
        };
        
        if (existingIndex >= 0) {
          // 更新现有信号
          const updated = [...prev];
          updated[existingIndex] = newSignal;
          return updated;
        } else {
          // 添加新信号
          return [newSignal, ...prev];
        }
      });
    });

    // 订阅交易更新
    const unsubscribeTrades = wsService.subscribeToTrades((trade) => {
      console.log('Received trade:', trade);
      setLatestTrade({
        symbol: trade.symbol,
        side: trade.side,
        price: trade.price.toFixed(2),
        time: new Date(trade.timestamp).toLocaleTimeString()
      });
    });

    return () => {
      unsubscribeSignals();
      unsubscribeTrades();
      // 不再取消市场数据订阅，由MarketContext管理
    };
  }, []);

  // 当marketData更新时，同步更新positions的PnL计算
  useEffect(() => {
    if (marketData.length > 0) {
      setPositions(prevPositions => {
        return prevPositions.map(pos => {
          const marketItem = marketData.find(m => m.symbol === pos.symbol);
          const currentPrice = marketItem ? parseFloat(marketItem.price) : pos.avgPrice;
          
          // 重新计算PnL
          const realizedPnl = (currentPrice - pos.avgPrice) * pos.amount;
          
          return {
            ...pos,
            currentPrice: currentPrice,
            pnl: realizedPnl
          };
        });
      });
    }
  }, [marketData]);

  // 定期刷新数据
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    setError(null);

    // 带超时的请求，超时后直接 reject，避免长时间阻塞首屏
    const REQUEST_TIMEOUT_MS = Number((import.meta as any).env?.VITE_REQUEST_TIMEOUT_MS) || 15000;
    const withTimeout = <T,>(p: Promise<T>, name: string, ms: number = REQUEST_TIMEOUT_MS): Promise<T> => {
      let settled = false;
      return Promise.race([
        p.then((res) => {
          settled = true;
          return res;
        }),
        new Promise<never>((_, reject) => {
          setTimeout(() => {
            if (!settled) {
              console.warn(`${name} 请求超过 ${ms}ms，自动降级为空数据`);
              reject(new Error('timeout'));
            }
          }, ms);
        }),
      ]);
    };

    // 1. 启动关键接口并并行执行
    const accountPromise   = withTimeout(apiService.getAccountSummary(), '账户数据', REQUEST_TIMEOUT_MS);
    const systemPromise    = withTimeout(apiService.getSystemStatus(),   '系统状态', REQUEST_TIMEOUT_MS);
    const positionsPromise = withTimeout(apiService.getPositions(),      '持仓数据', REQUEST_TIMEOUT_MS);

    // 2. 启动非关键接口（不阻塞首屏渲染）
    withTimeout(getCurrentSignals(), '信号数据')
      .then((data) => setCurrentSignals(data))
      .catch((err) => { handleError(err, '信号数据'); setCurrentSignals([]); });

    withTimeout(apiService.getEquityData(selectedTimeframe), '资金曲线')
      .then((res) => setEquityData(res.data || []))
      .catch((err) => { handleError(err, '资金曲线'); setEquityData([]); });

    // 3. 等待关键数据到达即可结束 loading
    const [accountRes, systemRes, positionsRes] = await Promise.allSettled([
      accountPromise,
      systemPromise,
      positionsPromise,
    ]);

    // 账户数据
    if (accountRes.status === 'fulfilled') {
      setAccountSummary(accountRes.value as AccountSummary);
    } else {
      handleError(accountRes.reason, '账户数据');
      setAccountSummary({
        balance: 0,
        daily_pnl: 0,
        total_pnl: 0,
        max_drawdown: 0,
        equity: 0,
        last_update: new Date().toISOString(),
      });
    }

    // 系统状态
    if (systemRes.status === 'fulfilled') {
      const sys = systemRes.value as SystemStatus;
      setSystemStatus(sys);
      if (sys.risk_block) setRiskAlert({ active: true, message: sys.risk_message });
    } else {
      handleError(systemRes.reason, '系统状态');
      setSystemStatus({
        mode: 'paper',
        latency_ms: 0,
        active_strategies: 0,
        total_strategies: 0,
        risk_block: false,
        risk_message: '',
        last_update: new Date().toISOString(),
        online: false,
        connection_status: 'disconnected',
      });
    }

    // 持仓数据
    if (positionsRes.status === 'fulfilled') {
      setPositions(transformPositions(positionsRes.value as Position[]));
    } else {
      handleError(positionsRes.reason, '持仓数据');
      setPositions([]);
    }

    setLoading(false);
  };

  const refreshData = async () => {
    try {
      // 分别刷新数据，避免一个失败导致全部失败
      try {
        const accountData = await apiService.getAccountSummary();
      setAccountSummary(accountData);
      } catch (error) {
        console.error('Failed to refresh account data:', error);
      }

      try {
        const systemData = await apiService.getSystemStatus();
      setSystemStatus(systemData);
      
      if (systemData.risk_block) {
        setRiskAlert({ active: true, message: systemData.risk_message });
      } else {
        setRiskAlert({ active: false, message: '' });
        }
      } catch (error) {
        console.error('Failed to refresh system status:', error);
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  };

  const transformPositions = (apiPositions: Position[]): DashboardPosition[] => {
    return apiPositions.map(pos => {
      // 从市场数据中获取当前价格
      const marketItem = marketData.find(m => m.symbol === pos.symbol);
      const currentPrice = marketItem ? parseFloat(marketItem.price) : pos.entry_price;
      
      // 重新计算PnL（如果有当前价格的话）
      const realizedPnl = marketItem 
        ? (currentPrice - pos.entry_price) * pos.qty 
        : pos.unrealized_pnl;
      
      return {
        symbol: pos.symbol,
        amount: pos.qty,
        avgPrice: pos.entry_price,
        currentPrice: currentPrice,
        pnl: realizedPnl,
        leverage: pos.leverage ?? 0,
        liqPrice: pos.liquidation_price ?? 0,
        margin: pos.margin ?? 0
      };
    });
  };

  const transformMarketData = (apiMarketData: MarketData[]): DashboardMarketData[] => {
    return apiMarketData.map((market) => {
      const price = market.price ?? 0;
      const change24 = market.change_24h ?? 0;
      const vol = market.volume_24h ?? 0;
      return {
        symbol: market.symbol,
        price: price.toFixed(2),
        change: `${change24 >= 0 ? '+' : ''}${change24.toFixed(2)}%`,
        changePercent: change24,
        volume: vol.toLocaleString(undefined, { maximumFractionDigits: 0 }),
        time: new Date().toLocaleTimeString(),
      } as DashboardMarketData;
    });
  };

  // 快捷操作函数
  const handleCloseAllPositions = async () => {
    try {
      const result = await apiService.closeAllPositions();
      message.success(result.message);
      // 刷新持仓数据
      const positionsData = await apiService.getPositions();
      setPositions(transformPositions(positionsData));
    } catch (error) {
      message.error('平仓失败');
    }
  };

  const handleToggleStrategies = async () => {
    try {
      if (systemStatus?.active_strategies === 0) {
        const result = await apiService.activateAllStrategies();
        message.success(result.message);
      } else {
        const result = await apiService.deactivateAllStrategies();
        message.success(result.message);
      }
      // 刷新系统状态
      const systemData = await apiService.getSystemStatus();
      setSystemStatus(systemData);
    } catch (error) {
      message.error('策略操作失败');
    }
  };

  const handleTestRisk = async () => {
    try {
      await apiService.triggerRiskAlert('测试风控警报');
      message.warning('风控测试已触发');
      // 刷新系统状态
      const systemData = await apiService.getSystemStatus();
      setSystemStatus(systemData);
      if (systemData.risk_block) {
        setRiskAlert({ active: true, message: systemData.risk_message });
      }
    } catch (error) {
      message.error('风控测试失败');
    }
  };

  const handleCloseRiskAlert = async () => {
    try {
      await apiService.clearRiskAlert();
      setRiskAlert({ active: false, message: '' });
      message.success('风险警报已清除');
    } catch (error) {
      message.error('清除警报失败');
    }
  };

  const handleTimeframeChange = async (timeframe: string) => {
    setSelectedTimeframe(timeframe);
    try {
      const equityDataResponse = await apiService.getEquityData(timeframe);
      setEquityData(equityDataResponse.data);
    } catch (error) {
      message.error('加载资金曲线失败');
    }
  };

  // 表格列定义
  const marketColumns: ColumnsType<DashboardMarketData> = [
    {
      title: '交易对',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (text: string) => (
        <span style={{ color: '#00d4ff', fontWeight: 'bold', fontFamily: 'Consolas' }}>
          {text}
        </span>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (text: string) => (
        <span style={{ color: '#ffffff', fontWeight: 'bold', fontFamily: 'Consolas' }}>
          ${text}
        </span>
      ),
    },
    {
      title: '24H成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (text: string) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>{text}</span>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'change',
      key: 'change',
      render: (text: string) => {
        const isPositive = text.startsWith('+');
        return (
          <span 
            className={isPositive ? 'price-positive' : 'price-negative'}
            style={{ fontFamily: 'Consolas' }}
          >
            {text}
          </span>
        );
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      render: (text: string) => (
        <span style={{ color: '#b0b0b0', fontFamily: 'Consolas' }}>
          {text}
        </span>
      ),
    },
  ];

  const positionColumns: ColumnsType<DashboardPosition> = [
    {
      title: '交易对',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (text: string) => (
        <span style={{ color: '#00d4ff', fontWeight: 'bold', fontFamily: 'Consolas' }}>
          {text}
        </span>
      ),
    },
    {
      title: '持仓数量',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>
          {value.toFixed(4)}
        </span>
      ),
    },
    {
      title: '平均价格',
      dataIndex: 'avgPrice',
      key: 'avgPrice',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>
          ${value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '当前价格',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>
          ${value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '杠杆',
      dataIndex: 'leverage',
      key: 'leverage',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>{value}x</span>
      ),
    },
    {
      title: '强平价',
      dataIndex: 'liqPrice',
      key: 'liqPrice',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>${value.toFixed(2)}</span>
      ),
    },
    {
      title: '保证金(USDT)',
      dataIndex: 'margin',
      key: 'margin',
      render: (value: number) => (
        <span style={{ color: '#ffffff', fontFamily: 'Consolas' }}>${value.toFixed(2)}</span>
      ),
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      render: (value: number) => {
        const isPositive = value >= 0;
        return (
          <span 
            className={isPositive ? 'price-positive' : 'price-negative'}
            style={{ fontFamily: 'Consolas' }}
          >
            {isPositive ? '+' : ''}${value.toFixed(2)}
          </span>
        );
      },
    },
  ];

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <Title level={3} style={{ color: '#ffffff', marginTop: '16px' }}>加载中...</Title>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="加载错误"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" danger onClick={loadInitialData}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  const getTradingModeTag = () => {
    const mode = systemStatus?.mode || 'paper';
    switch (mode) {
      case 'paper':
        return <Tag color="blue" icon={<ApiOutlined />}>模拟模式</Tag>;
      case 'testnet':
        return <Tag color="orange" icon={<ExclamationCircleOutlined />}>🧪 TESTNET</Tag>;
      case 'live':
        return <Tag color="red" icon={<ExclamationCircleOutlined />}>⚡ LIVE</Tag>;
      default:
        return <Tag color="default">未知模式</Tag>;
    }
  };

  const getConnectionStatusTag = () => {
    const status = systemStatus?.connection_status || 'disconnected';
    switch (status) {
      case 'connected':
        return <Tag color="green" icon={<CheckCircleOutlined />}>已连接</Tag>;
      case 'error':
        return <Tag color="red" icon={<ExclamationCircleOutlined />}>连接错误</Tag>;
      default:
        return <Tag color="default">未连接</Tag>;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 交易环境状态栏 */}
      <Row style={{ marginBottom: '16px' }}>
        <Col span={24}>
          <Card 
            style={{ 
              background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
              border: '1px solid #333',
              borderRadius: '8px'
            }}
            styles={{ body: { padding: '12px 20px' } }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space size="large">
                <div>
                  <span style={{ color: '#888', marginRight: '8px' }}>交易环境:</span>
                  {getTradingModeTag()}
                </div>
                <div>
                  <span style={{ color: '#888', marginRight: '8px' }}>连接状态:</span>
                  {getConnectionStatusTag()}
                </div>
                <div>
                  <span style={{ color: '#888', marginRight: '8px' }}>延迟:</span>
                  <span style={{ color: '#00d4ff', fontFamily: 'Consolas' }}>
                    {systemStatus?.latency_ms?.toFixed(1) || '0.0'}ms
                  </span>
                </div>
              </Space>
              <Space>
                {systemStatus?.mode === 'testnet' && (
                  <Alert
                    message="测试环境"
                    description="当前使用Binance Testnet，所有交易均为模拟"
                    type="warning"
                    showIcon
                    style={{ 
                      background: '#3d2914', 
                      border: '1px solid #d48806',
                      margin: 0,
                      padding: '4px 8px'
                    }}
                  />
                )}
                {systemStatus?.mode === 'live' && (
                  <Alert
                    message="实盘环境"
                    description="当前使用真实资金交易，请谨慎操作"
                    type="error"
                    showIcon
                    style={{ 
                      background: '#2a1215', 
                      border: '1px solid #a8071a',
                      margin: 0,
                      padding: '4px 8px'
                    }}
                  />
                )}
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 风控预警Banner */}
      {riskAlert.active && (
        <div className="risk-alert-banner">
          <div className="alert-content">
            <span className="alert-icon">⚠️</span>
            <span className="alert-text">{riskAlert.message}</span>
            <button className="alert-close" onClick={handleCloseRiskAlert}>×</button>
          </div>
        </div>
      )}

      {/* 快捷操作面板 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <div className="quick-actions">
            <Button 
              className="quick-btn emergency" 
              onClick={handleCloseAllPositions}
            >
              🚨 平仓所有
            </Button>
            <Button 
              className={`quick-btn ${systemStatus?.active_strategies === 0 ? 'start' : 'stop'}`}
              onClick={handleToggleStrategies}
            >
              {systemStatus?.active_strategies === 0 ? '▶️ 启动策略' : '⏸️ 停止策略'}
            </Button>
            <Button 
              className="quick-btn test" 
              onClick={handleTestRisk}
            >
              ⚠️ 测试风控
            </Button>
            <div className="strategy-status">
              <span className="status-text">
                策略状态: {systemStatus?.active_strategies || 0}/{systemStatus?.total_strategies || 0} 运行中
              </span>
            </div>
          </div>
        </Col>
      </Row>

      {/* 账户指标卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card className="metric-card">
            <Statistic
              title="账户余额"
              value={accountSummary?.balance}
              precision={2}
              prefix="$"
              valueStyle={{ color: '#00ff88', fontSize: '24px' }}
              formatter={(value) => {
                if (loading) return '加载中...';
                if (!accountSummary) return '--';
                return Number(value).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                });
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card className="metric-card">
            <Statistic
              title="今日盈亏"
              value={accountSummary?.daily_pnl}
              precision={2}
              prefix={accountSummary && accountSummary.daily_pnl >= 0 ? '+$' : '-$'}
              valueStyle={{ 
                color: accountSummary && accountSummary.daily_pnl >= 0 ? '#00ff88' : '#ff4757',
                fontSize: '24px'
              }}
              formatter={(value) => {
                if (loading) return '加载中...';
                if (!accountSummary) return '--';
                return Math.abs(Number(value)).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                });
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card className="metric-card">
            <Statistic
              title="总盈亏"
              value={accountSummary?.total_pnl}
              precision={2}
              prefix={accountSummary && accountSummary.total_pnl >= 0 ? '+$' : '-$'}
              valueStyle={{ 
                color: accountSummary && accountSummary.total_pnl >= 0 ? '#00ff88' : '#ff4757',
                fontSize: '24px'
              }}
              formatter={(value) => {
                if (loading) return '加载中...';
                if (!accountSummary) return '--';
                return Math.abs(Number(value)).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                });
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card className="metric-card">
            <Statistic
              title="最大回撤"
              value={accountSummary?.max_drawdown}
              precision={2}
              prefix="-$"
              valueStyle={{ color: '#ff4757', fontSize: '24px' }}
              formatter={(value) => {
                if (loading) return '加载中...';
                if (!accountSummary) return '--';
                return Math.abs(Number(value)).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                });
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 资金曲线图 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card 
            title={<span style={{ color: '#ffffff', fontSize: '16px' }}>资金曲线</span>}
            style={{ background: 'var(--bg-card)' }}
          >
            <EquityCurveChart 
              data={equityData}
              timeframe={selectedTimeframe}
              onTimeframeChange={handleTimeframeChange}
            />
          </Card>
        </Col>
      </Row>

      {/* 当前信号 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Title level={4} style={{ color: '#ffffff', marginBottom: '16px' }}>当前信号</Title>
          {currentSignals.length === 0 ? (
            <Alert
              message="没有启用的策略交易对"
              description="请先在策略页面启用一些策略来查看信号"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
          ) : (
            <Row gutter={16}>
              {currentSignals.map((signal, index) => {
                const signalText = signal.signal === 1 ? '做多' : signal.signal === -1 ? '做空' : '观望';
                const signalType = signal.signal === 1 ? 'long' : signal.signal === -1 ? 'short' : 'flat';
                const strengthPercent = Math.round(signal.strength * 100);
                
                return (
                  <Col span={24} key={`${signal.symbol}-${index}`}>
                    <div className={`signal-card signal-${signalType}`}>
                      <div className="signal-header">
                        <span className="signal-symbol">{signal.symbol}</span>
                        <span className="signal-time">{dayjs(signal.updated_at).format('HH:mm:ss')}</span>
                      </div>
                      <div className="signal-body">
                        <div className={`signal-badge signal-${signalType}`}>
                          {signalText}
                        </div>
                        <div className="confidence-bar">
                          <span className="confidence-label">信号强度</span>
                          <div className="confidence-progress">
                            <div 
                              className={`confidence-fill confidence-${signalType}`}
                              style={{ width: `${strengthPercent}%` }}
                            ></div>
                          </div>
                          <span className="confidence-value">{strengthPercent}%</span>
                        </div>
                      </div>
                    </div>
                  </Col>
                );
              })}
            </Row>
          )}
        </Col>
      </Row>

      {/* 实时行情和持仓表格 */}
      <Row gutter={[0, 16]}>
        <Col span={24}>
          <Card 
            title={<span style={{ color: '#ffffff' }}>实时行情</span>}
            style={{ background: 'var(--bg-card)' }}
          >
            <Table
              columns={marketColumns}
              dataSource={marketData}
              pagination={false}
              size="small"
              rowKey="symbol"
            />
          </Card>
        </Col>
        <Col span={24}>
          <Card 
            title={<span style={{ color: '#ffffff' }}>当前持仓</span>}
            style={{ background: 'var(--bg-card)' }}
          >
            <Table
              columns={positionColumns}
              dataSource={positions}
              pagination={false}
              size="small"
              rowKey="symbol"
              locale={{
                emptyText: (
                  <div style={{ color: '#b0b0b0', padding: '20px' }}>
                    {loading ? '加载中...' : '暂无持仓'}
                  </div>
                )
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
