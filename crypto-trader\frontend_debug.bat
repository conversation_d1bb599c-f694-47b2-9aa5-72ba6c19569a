@echo off

echo ========================================
echo     Frontend Debug Script
echo ========================================

echo Step 1: Basic setup
echo Current directory: %CD%
pause

echo Step 2: Test setlocal command
setlocal
echo setlocal command successful
pause

echo Step 3: Test Node.js commands
echo Testing node --version
node --version
echo Testing npm --version  
npm --version
pause

echo Step 4: Test where command with error handling
echo Testing where node with error handling
where node >nul 2>&1
if errorlevel 1 (
    echo Node not found via where command
) else (
    echo Node found via where command
)
pause

echo Step 5: Test PowerShell command
echo Testing PowerShell connectivity check
powershell -Command "Write-Host 'PowerShell test successful'"
pause

echo Step 6: Test complex PowerShell command
echo Testing backend connectivity check
powershell -Command "try { Write-Host 'Testing connection...'; exit 0 } catch { Write-Host 'Error occurred'; exit 1 }" 
echo PowerShell exit code: %errorlevel%
pause

echo Step 7: Test directory operations
echo Current directory: %CD%
if exist "frontend" (
    echo Frontend directory exists
    cd frontend
    echo Switched to: %CD%
    if exist "package.json" (
        echo package.json exists
    ) else (
        echo package.json NOT found
    )
    cd ..
    echo Back to: %CD%
) else (
    echo Frontend directory NOT found
)
pause

echo Step 8: Test npm command
cd frontend
echo Testing npm command in frontend directory
npm --version
pause

echo All debug steps completed successfully!
echo The issue might be in the specific PowerShell backend check
pause
