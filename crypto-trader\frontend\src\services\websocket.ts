// WebSocket service for real-time data
// WebSocket base URL；可通过 VITE_WS_URL 覆盖，默认使用 localhost
const WS_BASE: string = (import.meta as any).env?.VITE_WS_URL || 'ws://localhost:8000';
// 推导出对应的 http(s) 协议地址，用于健康检查等 REST 调用
const HTTP_BASE: string = WS_BASE.replace(/^ws/i, 'http');
const HEALTH_TIMEOUT_MS = Number((import.meta as any).env?.VITE_HEALTH_TIMEOUT_MS) || 5000;

export interface SignalUpdate {
  type: 'signal';
  data: {
    symbol: string;
    signal: number;        // 1=LONG, 0=FLAT, -1=SHORT
    strength: number;      // 信号强度 0-1
    strategy_name: string;
    updated_at: string;
  };
}

export interface TradeUpdate {
  type: 'trade';
  data: {
    symbol: string;
    side: string;
    price: number;
    qty: number;
    trade_id?: string;
    timestamp: string;
  };
}

export interface MarketUpdate {
  type: 'market';
  data: {
    symbol: string;
    price: number;
    volume: number;
    timestamp: string;
  };
}

export interface MarketData {
  symbol: string;
  price: number;
  change_24h: number;
  volume_24h: number;
}

export type WebSocketMessage = SignalUpdate | TradeUpdate | MarketUpdate;

class WebSocketService {
  private connections: Map<string, WebSocket> = new Map();
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private reconnectTimers: Map<string, number> = new Map();
  private maxReconnectAttempts = 3; // 减少重连次数
  private reconnectDelay = 5000; // 增加重连延迟
  private heartbeatInterval = 30000; // 30秒心跳
  private heartbeatTimers: Map<string, number> = new Map();
  private isConnecting: Map<string, boolean> = new Map(); // 防止重复连接
  
  // 新增：连接状态监听器
  private connectionListeners: Set<(connected: boolean) => void> = new Set();
  // 新增：通用消息监听器
  private messageListeners: Set<(data: any) => void> = new Set();

  connect(channel: string, onMessage: (data: any) => void): void {
    // Add listener
    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, new Set());
    }
    this.listeners.get(channel)!.add(onMessage);

    // If connection already exists and is open, don't create new one
    const existingWs = this.connections.get(channel);
    if (existingWs && existingWs.readyState === WebSocket.OPEN) {
      return;
    }

    // 防止重复连接
    if (this.isConnecting.get(channel)) {
      console.log(`⏳ Already connecting to ${channel}, skipping...`);
      return;
    }

    // 兼容后端不同通道路径
    let wsPath: string;
    switch (channel) {
      case 'market':
        wsPath = '/api/v1/market/ws';
        break;

      default:
        wsPath = `/ws/${channel}`; // 默认路径
    }
    const wsUrl = `${WS_BASE}${wsPath}`;
    console.log(`🔗 Connecting to WebSocket: ${wsUrl}`);
    
    this.isConnecting.set(channel, true);
    
    try {
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`✅ WebSocket connected to ${channel}`);
        this.reconnectAttempts.set(channel, 0);
        this.isConnecting.set(channel, false);
        this.startHeartbeat(channel, ws);
        // 通知连接状态监听器
        this.notifyConnectionChange(true);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          // Notify all listeners for this channel
          const channelListeners = this.listeners.get(channel);
          if (channelListeners) {
            channelListeners.forEach(listener => listener(data));
          }
          // 通知通用消息监听器
          this.messageListeners.forEach(listener => listener(data));
        } catch (error) {
          console.error(`Error parsing WebSocket message for ${channel}:`, error);
        }
      };

      ws.onclose = (event) => {
        console.log(`🔌 WebSocket disconnected from ${channel} (code: ${event.code}, reason: ${event.reason})`);
        this.connections.delete(channel);
        this.isConnecting.set(channel, false);
        this.stopHeartbeat(channel);
        // 通知连接状态监听器
        this.notifyConnectionChange(false);
        
        // 只有在非正常关闭时才尝试重连
        if (event.code !== 1000 && event.code !== 1001) {
        const attempts = this.reconnectAttempts.get(channel) || 0;
        if (attempts < this.maxReconnectAttempts) {
          console.log(`🔄 Attempting to reconnect to ${channel} (${attempts + 1}/${this.maxReconnectAttempts})`);
          this.reconnectAttempts.set(channel, attempts + 1);
          
          const timer = setTimeout(() => {
            // Re-establish connection for all listeners
            const channelListeners = this.listeners.get(channel);
            if (channelListeners && channelListeners.size > 0) {
              // Get first listener to trigger reconnection
              const firstListener = channelListeners.values().next().value;
              if (firstListener) {
                this.connect(channel, firstListener);
              }
            }
          }, this.reconnectDelay * Math.pow(2, attempts)); // 指数退避
          
          this.reconnectTimers.set(channel, timer);
        } else {
            console.warn(`⚠️ Max reconnection attempts reached for ${channel}, giving up`);
          }
        }
      };

      ws.onerror = (error) => {
        console.warn(`⚠️ WebSocket error on ${channel}:`, error);
        this.isConnecting.set(channel, false);
        
        // 如果是连接错误，记录但不立即重连（由onclose处理）
        if (ws.readyState === WebSocket.CONNECTING) {
          console.warn(`⚠️ Connection failed for ${channel}, will be handled by onclose`);
        }
      };

      this.connections.set(channel, ws);
    } catch (error) {
      console.warn(`⚠️ Failed to create WebSocket connection for ${channel}:`, error);
      this.isConnecting.set(channel, false);
    }
  }

  disconnect(channel: string, onMessage?: (data: any) => void, force: boolean = false): void {
    if (onMessage && !force) {
      // Remove specific listener
      const channelListeners = this.listeners.get(channel);
      if (channelListeners) {
        channelListeners.delete(onMessage);
        
        // If no more listeners and not forced, close connection
        if (channelListeners.size === 0) {
          this.listeners.delete(channel);
          const ws = this.connections.get(channel);
          if (ws) {
            ws.close();
            this.connections.delete(channel);
          }
        }
      }
    } else {
      // Remove all listeners and close connection (or force close)
      this.listeners.delete(channel);
      const ws = this.connections.get(channel);
      if (ws) {
        ws.close();
        this.connections.delete(channel);
      }
    }
  }

  disconnectAll(): void {
    for (const [channel, ws] of this.connections) {
      ws.close();
    }
    this.connections.clear();
    this.listeners.clear();
    this.reconnectAttempts.clear();
  }

  // 新增：连接状态监听
  onConnectionChange(listener: (connected: boolean) => void): void {
    this.connectionListeners.add(listener);
  }

  offConnectionChange(listener: (connected: boolean) => void): void {
    this.connectionListeners.delete(listener);
  }

  private notifyConnectionChange(connected: boolean): void {
    this.connectionListeners.forEach(listener => listener(connected));
  }

  // 新增：通用消息监听
  onMessage(listener: (data: any) => void): void {
    this.messageListeners.add(listener);
  }

  offMessage(listener: (data: any) => void): void {
    this.messageListeners.delete(listener);
  }

  // Convenience methods for specific channels
  subscribeToSignals(onSignal: (signal: SignalUpdate['data']) => void): () => void {
    const handler = (data: SignalUpdate) => {
      if (data.type === 'signal') {
        onSignal(data.data);
      }
    };
    
    // 使用智能连接
    this.smartConnect('signal', handler).catch(error => {
      console.warn('⚠️ Failed to establish smart connection to signals:', error);
    });
    
    return () => this.disconnect('signal', handler);
  }

  subscribeToTrades(onTrade: (trade: TradeUpdate['data']) => void): () => void {
    const handler = (data: TradeUpdate) => {
      if (data.type === 'trade') {
        onTrade(data.data);
      }
    };
    
    this.connect('trade', handler);
    
    return () => this.disconnect('trade', handler);
  }

  // 修改：支持新的MarketData格式
  subscribeToMarket(onMarket?: (market: MarketData) => void): () => void {
    const handler = (data: any) => {
      if (data.type === 'market_data' && Array.isArray(data.data)) {
        // 批量数据，逐个调用回调
        if (onMarket) {
          data.data.forEach((item: MarketData) => onMarket(item));
        }
      } else if (data.type === 'market') {
        // 单个数据，转换格式
        const marketData: MarketData = {
          symbol: data.data.symbol,
          price: data.data.price,
          change_24h: 0, // 旧格式没有这个字段
          volume_24h: data.data.volume || 0
        };
        if (onMarket) {
          onMarket(marketData);
        }
      }
    };
    
    // 使用正确的市场数据WebSocket路径
    const wsUrl = `${WS_BASE}/ws/market`;
    console.log(`🔗 Connecting to Market WebSocket: ${wsUrl}`);
    
    try {
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`✅ Market WebSocket connected`);
        this.notifyConnectionChange(true);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handler(data);
          // 通知通用消息监听器
          this.messageListeners.forEach(listener => listener(data));
        } catch (error) {
          console.error(`Error parsing Market WebSocket message:`, error);
        }
      };

      ws.onclose = (event) => {
        console.log(`🔌 Market WebSocket disconnected (code: ${event.code}, reason: ${event.reason})`);
        this.notifyConnectionChange(false);
      };

      ws.onerror = (error) => {
        console.error(`❌ Market WebSocket error:`, error);
      };

      this.connections.set('market', ws);
    } catch (error) {
      console.error(`❌ Failed to create Market WebSocket connection:`, error);
    }
    
    return () => this.disconnect('market', handler);
  }

  // 新增：简化的市场数据订阅方法（不需要回调）
  connectToMarket(): void {
    // 检查是否已经连接
    const existingWs = this.connections.get('market');
    if (existingWs && existingWs.readyState === WebSocket.OPEN) {
      console.log('Market WebSocket already connected');
      return;
    }

    // 使用正确的市场数据WebSocket路径
    const wsUrl = `${WS_BASE}/ws/market`;
    console.log(`🔗 Connecting to Market WebSocket: ${wsUrl}`);
    
    try {
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`✅ Market WebSocket connected`);
        this.notifyConnectionChange(true);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          // 通知通用消息监听器
          this.messageListeners.forEach(listener => listener(data));
        } catch (error) {
          console.error(`Error parsing Market WebSocket message:`, error);
        }
      };

      ws.onclose = (event) => {
        console.log(`🔌 Market WebSocket disconnected (code: ${event.code}, reason: ${event.reason})`);
        this.notifyConnectionChange(false);
      };

      ws.onerror = (error) => {
        console.error(`❌ Market WebSocket error:`, error);
      };

      this.connections.set('market', ws);
    } catch (error) {
      console.error(`❌ Failed to create Market WebSocket connection:`, error);
    }
  }

  // 新增：断开市场数据连接
  disconnectFromMarket(): void {
    const ws = this.connections.get('market');
    if (ws) {
      ws.close();
      this.connections.delete('market');
      this.notifyConnectionChange(false);
      console.log('Market WebSocket disconnected');
    }
  }

  private startHeartbeat(channel: string, ws: WebSocket): void {
    // 清除之前的心跳
    const existingTimer = this.heartbeatTimers.get(channel);
    if (existingTimer) {
      clearInterval(existingTimer);
    }

    // 启动新的心跳
    const timer = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
      } else {
        this.stopHeartbeat(channel);
      }
    }, this.heartbeatInterval);

    this.heartbeatTimers.set(channel, timer);
  }

  private stopHeartbeat(channel: string): void {
    const timer = this.heartbeatTimers.get(channel);
    if (timer) {
      clearInterval(timer);
      this.heartbeatTimers.delete(channel);
    }
  }

  getConnectionStatus(channel: string): 'connecting' | 'open' | 'closed' {
    const ws = this.connections.get(channel);
    if (!ws) return 'closed';
    
    switch (ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'open';
      default:
        return 'closed';
    }
  }

  // 新增：后端健康检查
  async checkBackendHealth(retry = 0): Promise<boolean> {
    try {
      // 使用更兼容的超时方式
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), HEALTH_TIMEOUT_MS);
      
      const response = await fetch(`${HTTP_BASE}/health`, {
        method: 'GET',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      if (retry < 2) {
        // 退避重试
        await new Promise(r => setTimeout(r, 1000 * (retry + 1)));
        return this.checkBackendHealth(retry + 1);
      }
      console.warn('⚠️ Backend health check failed:', error);
      return false;
    }
  }

  // 新增：智能连接（先检查后端健康状态）
  async smartConnect(channel: string, onMessage: (data: any) => void): Promise<void> {
    const isHealthy = await this.checkBackendHealth();
    if (isHealthy) {
      this.connect(channel, onMessage);
    } else {
      console.warn(`⚠️ Backend not available, skipping WebSocket connection to ${channel}`);
    }
  }
}

export const wsService = new WebSocketService(); 