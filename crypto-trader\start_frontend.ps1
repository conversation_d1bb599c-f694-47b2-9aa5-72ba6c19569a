Write-Host "========================================" -ForegroundColor Green
Write-Host "     Crypto Trader Frontend Startup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "[INFO] Current directory: $(Get-Location)" -ForegroundColor Cyan

Write-Host ""
Write-Host "[CHECK] Checking Node.js environment..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "[SUCCESS] Node.js found: $nodeVersion" -ForegroundColor Green
    Write-Host "[SUCCESS] npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Node.js not found" -ForegroundColor Red
    Write-Host "[TIP] Please install Node.js 16+" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[CHECK] Checking backend service connection..." -ForegroundColor Yellow
Write-Host "[INFO] Trying to connect to backend server http://localhost:8000/health" -ForegroundColor Cyan

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "[SUCCESS] Successfully connected to backend server!" -ForegroundColor Green
        Write-Host "[INFO] Backend service running normally, all features available" -ForegroundColor Cyan
    }
} catch {
    Write-Host "[WARNING] Cannot connect to backend server" -ForegroundColor Yellow
    Write-Host "[TIP] Please ensure backend server is running" -ForegroundColor Yellow
    Write-Host "[TIP] Run backend_start.bat to start backend" -ForegroundColor Yellow
    Write-Host "[TIP] Backend address: http://localhost:8000" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "[CHOICE] Continue starting frontend?" -ForegroundColor Yellow
    Write-Host "[TIP] Frontend features limited without backend" -ForegroundColor Yellow
    $continue = Read-Host "Press Enter to continue, or Ctrl+C to cancel"
}

Write-Host ""
Write-Host "[CHECK] Checking frontend directory..." -ForegroundColor Yellow

if (-not (Test-Path "frontend")) {
    Write-Host "[ERROR] Frontend directory not found" -ForegroundColor Red
    Write-Host "[TIP] Please run this script from crypto-trader root directory" -ForegroundColor Yellow
    Write-Host "[DEBUG] Current directory contents:" -ForegroundColor Cyan
    Get-ChildItem | Select-Object Name
    Read-Host "Press Enter to exit"
    exit 1
}

Set-Location frontend
Write-Host "[INFO] Switched to frontend directory: $(Get-Location)" -ForegroundColor Cyan

if (-not (Test-Path "package.json")) {
    Write-Host "[ERROR] package.json file not found" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "[SUCCESS] package.json file check passed" -ForegroundColor Green

Write-Host ""
Write-Host "[CHECK] Checking frontend dependencies..." -ForegroundColor Yellow

if (-not (Test-Path "node_modules")) {
    Write-Host "[INFO] First run, installing frontend dependencies..." -ForegroundColor Cyan
    Write-Host "[INFO] This may take a few minutes, please wait..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Frontend dependencies installation failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "[SUCCESS] Frontend dependencies installation completed" -ForegroundColor Green
} else {
    Write-Host "[SUCCESS] Frontend dependencies already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "[CHECK] Checking key files..." -ForegroundColor Yellow

if (-not (Test-Path "src\App.tsx")) {
    Write-Host "[ERROR] Missing key files" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "[SUCCESS] Key files check passed" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "     Starting Frontend Development Server" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "[INFO] Starting Vite development server..." -ForegroundColor Cyan
Write-Host "[INFO] Frontend URL: http://localhost:5173" -ForegroundColor Cyan
Write-Host "[INFO] Backend API: http://localhost:8000" -ForegroundColor Cyan
Write-Host ""
Write-Host "[SUCCESS] Frontend development server starting!" -ForegroundColor Green
Write-Host "[TIP] Browser will automatically open trading interface" -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop development server" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

npm run dev

Write-Host ""
Write-Host "[INFO] Frontend development server stopped" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
