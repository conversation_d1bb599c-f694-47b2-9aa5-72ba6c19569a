from decimal import Decimal
from typing import Op<PERSON>, <PERSON><PERSON>, Union

from .. import BaseProvider

localized = True

PlaceType = Tuple[str, str, str, str, str]


class Provider(BaseProvider):
    """
    land_coords data extracted from geonames.org, under the Creative Commons Attribution 3.0 License.
    Coordinates are in decimal format for mapping purposes.
    Country code is in Alpha 2 format (https://www.nationsonline.org/oneworld/country_code_list.htm).
    Timezones are canonical (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones).
    """

    land_coords: Tuple[PlaceType, ...] = (
        ("42.50729", "1.53414", "les Escaldes", "AD", "Europe/Andorra"),
        ("36.21544", "65.93249", "<PERSON><PERSON>-e <PERSON>", "AF", "Asia/Kabul"),
        ("40.49748", "44.7662", "Hrazdan", "AM", "Asia/Yerevan"),
        ("-11.78333", "19.91667", "<PERSON><PERSON>", "<PERSON><PERSON>", "Africa/Luanda"),
        ("-37.32167", "-59.13316", "Tan<PERSON><PERSON>", "AR", "America/Argentina/Buenos_Aires"),
        (
            "-34.74785",
            "-58.70072",
            "Pontevedra",
            "AR",
            "America/Argentina/Buenos_Aires",
        ),
        ("-34.64966", "-58.38341", "Barracas", "AR", "America/Argentina/Buenos_Aires"),
        ("-54.8", "-68.3", "Ushuaia", "AR", "America/Argentina/Ushuaia"),
        ("-31.25033", "-61.4867", "Rafaela", "AR", "America/Argentina/Cordoba"),
        ("-31.4488", "-60.93173", "Esperanza", "AR", "America/Argentina/Cordoba"),
        ("-34.64167", "-60.47389", "Chacabuco", "AR", "America/Argentina/Buenos_Aires"),
        ("-27.4338", "-65.61427", "Aguilares", "AR", "America/Argentina/Tucuman"),
        ("47.05", "15.46667", "Sankt Peter", "AT", "Europe/Vienna"),
        ("48.25", "16.4", "Floridsdorf", "AT", "Europe/Vienna"),
        ("-31.95224", "115.8614", "Perth", "AU", "Australia/Perth"),
        ("-37.9", "145.18333", "Wheelers Hill", "AU", "Australia/Melbourne"),
        ("-33.88096", "151.07986", "Strathfield", "AU", "Australia/Sydney"),
        ("-34.88422", "150.60036", "Nowra", "AU", "Australia/Sydney"),
        ("-25.54073", "152.70493", "Maryborough", "AU", "Australia/Brisbane"),
        ("-34.28853", "146.05093", "Griffith", "AU", "Australia/Sydney"),
        ("-33.79176", "151.08057", "Eastwood", "AU", "Australia/Sydney"),
        ("-37.88333", "145.06667", "Carnegie", "AU", "Australia/Melbourne"),
        ("-33.75881", "150.99292", "Baulkham Hills", "AU", "Australia/Sydney"),
        ("-27.50578", "153.10236", "Carindale", "AU", "Australia/Brisbane"),
        ("-32.05251", "115.88782", "Willetton", "AU", "Australia/Perth"),
        ("-38.16604", "145.13643", "Frankston South", "AU", "Australia/Melbourne"),
        ("38.45598", "48.87498", "Astara", "AZ", "Asia/Baku"),
        ("41.09246", "45.36561", "Qazax", "AZ", "Asia/Baku"),
        ("44.75874", "19.21437", "Bijeljina", "BA", "Europe/Sarajevo"),
        ("23.9028", "89.11943", "Kushtia", "BD", "Asia/Dhaka"),
        ("22.83957", "91.84128", "Manikchari", "BD", "Asia/Dhaka"),
        ("50.8", "3.16667", "Wevelgem", "BE", "Europe/Brussels"),
        ("51.12794", "4.21372", "Temse", "BE", "Europe/Brussels"),
        ("50.71229", "4.52529", "Rixensart", "BE", "Europe/Brussels"),
        ("50.74497", "3.20639", "Mouscron", "BE", "Europe/Brussels"),
        ("51.24197", "4.82313", "Lille", "BE", "Europe/Brussels"),
        ("51.03427", "5.37429", "Houthalen", "BE", "Europe/Brussels"),
        ("50.56149", "4.69889", "Gembloux", "BE", "Europe/Brussels"),
        ("50.88506", "4.07601", "Denderleeuw", "BE", "Europe/Brussels"),
        ("51.21187", "4.25633", "Beveren", "BE", "Europe/Brussels"),
        ("41.57439", "24.71204", "Smolyan", "BG", "Europe/Sofia"),
        ("43.4125", "23.225", "Montana", "BG", "Europe/Sofia"),
        ("42.7", "27.25", "Aytos", "BG", "Europe/Sofia"),
        ("8.88649", "2.59753", "Tchaourou", "BJ", "Africa/Porto-Novo"),
        ("-21.44345", "-65.71875", "Tupiza", "BO", "America/La_Paz"),
        ("-0.71667", "-48.52333", "Soure", "BR", "America/Belem"),
        ("-8.05389", "-34.88111", "Recife", "BR", "America/Recife"),
        ("-4.42472", "-41.45861", "Pedro II", "BR", "America/Fortaleza"),
        ("-3.14306", "-58.44417", "Itacoatiara", "BR", "America/Manaus"),
        ("-4.16694", "-40.7475", "Guaraciaba do Norte", "BR", "America/Fortaleza"),
        ("-8.66667", "-35.71667", "Catende", "BR", "America/Recife"),
        ("-8.28333", "-35.03333", "Cabo", "BR", "America/Recife"),
        ("-4.24444", "-42.29444", "Barras", "BR", "America/Fortaleza"),
        ("-3.20333", "-52.20639", "Altamira", "BR", "America/Santarem"),
        ("-20.87306", "-48.29694", "Viradouro", "BR", "America/Sao_Paulo"),
        ("-22.97056", "-46.99583", "Valinhos", "BR", "America/Sao_Paulo"),
        ("-10.95817", "-38.79084", "Tucano", "BR", "America/Bahia"),
        ("-28.81833", "-52.51028", "Soledade", "BR", "America/Sao_Paulo"),
        ("-23.44361", "-51.87389", "Sarandi", "BR", "America/Sao_Paulo"),
        ("-22.45667", "-47.53028", "Santa Gertrudes", "BR", "America/Sao_Paulo"),
        ("-11.48472", "-37.93278", "Rio Real", "BR", "America/Bahia"),
        ("-19.32556", "-41.25528", "Resplendor", "BR", "America/Sao_Paulo"),
        ("-26.22861", "-52.67056", "Pato Branco", "BR", "America/Sao_Paulo"),
        ("-25.42944", "-50.00639", "Palmeira", "BR", "America/Sao_Paulo"),
        ("-12.91667", "-39.25", "Muritiba", "BR", "America/Bahia"),
        ("-21.41222", "-42.19667", "Miracema", "BR", "America/Sao_Paulo"),
        ("-28.44917", "-52.2", "Marau", "BR", "America/Sao_Paulo"),
        ("-22.92306", "-53.13722", "Loanda", "BR", "America/Sao_Paulo"),
        ("-10.91722", "-37.65", "Lagarto", "BR", "America/Maceio"),
        ("-19.72806", "-50.19556", "Iturama", "BR", "America/Sao_Paulo"),
        ("-21.205", "-41.88778", "Itaperuna", "BR", "America/Sao_Paulo"),
        ("-20.25333", "-43.80139", "Itabirito", "BR", "America/Sao_Paulo"),
        ("-28.24", "-48.67028", "Imbituba", "BR", "America/Sao_Paulo"),
        ("-22.53722", "-42.98194", "Guapimirim", "BR", "America/Sao_Paulo"),
        ("-19.7625", "-44.31389", "Esmeraldas", "BR", "America/Sao_Paulo"),
        ("-25.42778", "-49.27306", "Curitiba", "BR", "America/Sao_Paulo"),
        ("-14.66463", "-52.35558", "Nova Xavantina", "BR", "America/Cuiaba"),
        ("-29.2975", "-51.50361", "Carlos Barbosa", "BR", "America/Sao_Paulo"),
        ("-15.675", "-38.94722", "Canavieiras", "BR", "America/Bahia"),
        ("-17.74431", "-48.62789", "Caldas Novas", "BR", "America/Sao_Paulo"),
        ("-23.7975", "-48.59278", "Buri", "BR", "America/Sao_Paulo"),
        ("-10.90889", "-37.03861", "Barra dos Coqueiros", "BR", "America/Maceio"),
        ("-22.57306", "-47.1725", "Artur Nogueira", "BR", "America/Sao_Paulo"),
        ("-10.91111", "-37.07167", "Aracaju", "BR", "America/Maceio"),
        ("-21.42917", "-45.94722", "Alfenas", "BR", "America/Sao_Paulo"),
        ("-8.76194", "-63.90389", "Porto Velho", "BR", "America/Porto_Velho"),
        ("-21.44236", "27.46153", "Tonota", "BW", "Africa/Gaborone"),
        ("55.1904", "30.2049", "Vitebsk", "BY", "Europe/Minsk"),
        ("53.5942", "25.8191", "Novogrudok", "BY", "Europe/Minsk"),
        ("52.4089", "31.3237", "Dobrush", "BY", "Europe/Minsk"),
        ("45.43341", "-73.86586", "Beaconsfield", "CA", "America/Toronto"),
        ("46.23899", "-63.13414", "Charlottetown", "CA", "America/Halifax"),
        ("45.4473", "-73.75335", "Dorval", "CA", "America/Toronto"),
        ("49.88307", "-119.48568", "Kelowna", "CA", "America/Vancouver"),
        ("43.86682", "-79.2663", "Markham", "CA", "America/Toronto"),
        ("42.8334", "-80.38297", "Norfolk County", "CA", "America/Toronto"),
        ("45.44868", "-73.81669", "Pointe-Claire", "CA", "America/Toronto"),
        ("45.40008", "-73.58248", "Sainte-Catherine", "CA", "America/Toronto"),
        ("53.51684", "-113.3187", "Sherwood Park", "CA", "America/Edmonton"),
        ("50.26729", "-119.27337", "Vernon", "CA", "America/Vancouver"),
        ("46.1351", "-60.1831", "Sydney", "CA", "America/Glace_Bay"),
        ("0.76755", "24.43973", "Yangambi", "CD", "Africa/Lubumbashi"),
        ("-8.73508", "24.99798", "Kamina", "CD", "Africa/Lubumbashi"),
        ("0.49113", "29.47306", "Beni", "CD", "Africa/Lubumbashi"),
        ("-4.5833", "15.16554", "Kasangulu", "CD", "Africa/Kinshasa"),
        ("4.94273", "15.87735", "Carnot", "CF", "Africa/Bangui"),
        ("-4.26613", "15.28318", "Brazzaville", "CG", "Africa/Brazzaville"),
        ("46.18396", "6.10237", "Onex", "CH", "Europe/Zurich"),
        ("47.30997", "8.52462", "Adliswil", "CH", "Europe/Zurich"),
        ("5.84752", "-5.682", "Lakota", "CI", "Africa/Abidjan"),
        ("5.27247", "-3.59625", "Bonoua", "CI", "Africa/Abidjan"),
        ("-33.59217", "-70.6996", "San Bernardo", "CL", "America/Santiago"),
        ("-30.60106", "-71.19901", "Ovalle", "CL", "America/Santiago"),
        ("-32.45242", "-71.23106", "La Ligua", "CL", "America/Santiago"),
        ("-36.9256", "-73.02841", "Chiguayante", "CL", "America/Santiago"),
        ("4.96667", "10.7", "Tonga", "CM", "Africa/Douala"),
        ("3.51667", "11.5", "Mbalmayo", "CM", "Africa/Douala"),
        ("4.2475", "9.00472", "Idenao", "CM", "Africa/Douala"),
        ("46.51872", "86.00214", "Hoxtolgay", "CN", "Asia/Urumqi"),
        ("36.81667", "117.81667", "Zhoucun", "CN", "Asia/Shanghai"),
        ("34.86472", "117.55417", "Zaozhuang", "CN", "Asia/Shanghai"),
        ("23.73333", "114.68333", "Heyuan", "CN", "Asia/Shanghai"),
        ("34.65918", "109.22921", "Yanliang", "CN", "Asia/Shanghai"),
        ("38.40917", "112.73333", "Xinzhou", "CN", "Asia/Shanghai"),
        ("33.78333", "114.51667", "Wacheng", "CN", "Asia/Shanghai"),
        ("27.85", "112.9", "Xiangtan", "CN", "Asia/Shanghai"),
        ("37.19723", "122.05228", "Tianfu", "CN", "Asia/Shanghai"),
        ("34.85", "117.33333", "Taozhuang", "CN", "Asia/Shanghai"),
        ("35.64889", "117.27583", "Sishui", "CN", "Asia/Shanghai"),
        ("27.34089", "117.4831", "Shaowu", "CN", "Asia/Shanghai"),
        ("37.30553", "120.82747", "Zhuangyuan", "CN", "Asia/Shanghai"),
        ("35.50056", "117.63083", "Pingyi", "CN", "Asia/Shanghai"),
        ("27.92333", "118.53333", "Pucheng", "CN", "Asia/Shanghai"),
        ("24.28859", "116.11768", "Meizhou", "CN", "Asia/Shanghai"),
        ("37.65181", "120.33063", "Longgang", "CN", "Asia/Shanghai"),
        ("23.29549", "113.82465", "Licheng", "CN", "Asia/Shanghai"),
        ("36.19278", "117.65694", "Laiwu", "CN", "Asia/Shanghai"),
        ("30.35028", "112.19028", "Jingzhou", "CN", "Asia/Shanghai"),
        ("32.50611", "120.14278", "Jiangyan", "CN", "Asia/Shanghai"),
        ("30.24706", "115.04814", "Huangshi", "CN", "Asia/Shanghai"),
        ("37.73222", "115.70111", "Hengshui", "CN", "Asia/Shanghai"),
        ("28.88162", "120.03308", "Guli", "CN", "Asia/Shanghai"),
        ("23.02677", "113.13148", "Foshan", "CN", "Asia/Shanghai"),
        ("35.85", "117.7", "Dongdu", "CN", "Asia/Shanghai"),
        ("32.54278", "111.50861", "Danjiangkou", "CN", "Asia/Shanghai"),
        ("35.20889", "111.73861", "Changzhi", "CN", "Asia/Shanghai"),
        ("34.56861", "105.89333", "Beidao", "CN", "Asia/Shanghai"),
        ("29.98869", "122.20488", "Zhoushan", "CN", "Asia/Shanghai"),
        ("40.66482", "122.22833", "Yingkou", "CN", "Asia/Shanghai"),
        ("46.08333", "122.08333", "Ulanhot", "CN", "Asia/Shanghai"),
        ("45.35", "126.28333", "Shuangcheng", "CN", "Asia/Shanghai"),
        ("41.09822", "120.74792", "Nanpiao", "CN", "Asia/Shanghai"),
        ("41.27194", "123.17306", "Liaoyang", "CN", "Asia/Shanghai"),
        ("41.94175", "123.50266", "Hushitai", "CN", "Asia/Shanghai"),
        ("40.85158", "122.74754", "Haicheng", "CN", "Asia/Shanghai"),
        ("42.64031", "125.51176", "Dongfeng", "CN", "Asia/Shanghai"),
        ("45.75279", "130.57211", "Boli", "CN", "Asia/Shanghai"),
        ("31.64615", "120.74221", "Changshu City", "CN", "Asia/Shanghai"),
        ("7.83389", "-72.47417", "Villa del Rosario", "CO", "America/Bogota"),
        ("6.46838", "-73.26022", "Socorro", "CO", "America/Bogota"),
        ("8.79577", "-75.69947", "San Carlos", "CO", "America/Bogota"),
        ("10.98778", "-74.95472", "Puerto Colombia", "CO", "America/Bogota"),
        ("4.73245", "-74.26419", "Madrid", "CO", "America/Bogota"),
        ("5.20856", "-74.73584", "Honda", "CO", "America/Bogota"),
        ("10.15031", "-73.9614", "El Copey", "CO", "America/Bogota"),
        ("3.8801", "-77.03116", "Buenaventura", "CO", "America/Bogota"),
        ("5.6561", "-75.87877", "Andes", "CO", "America/Bogota"),
        ("9.92787", "-84.13722", "San Rafael", "CR", "America/Costa_Rica"),
        ("10.63504", "-85.43772", "Liberia", "CR", "America/Costa_Rica"),
        ("23.15678", "-81.24441", "Varadero", "CU", "America/Havana"),
        ("20.14298", "-77.43532", "Media Luna", "CU", "America/Havana"),
        ("23.04419", "-82.00919", "Jaruco", "CU", "America/Havana"),
        ("22.98212", "-80.58556", "Corralillo", "CU", "America/Havana"),
        ("23.0072", "-82.4017", "Boyeros", "CU", "America/Havana"),
        ("50.50301", "13.63617", "Most", "CZ", "Europe/Prague"),
        ("50.23271", "12.87117", "Karlovy Vary", "CZ", "Europe/Prague"),
        ("50.073658", "14.418540", "Praha", "CZ", "Europe/Prague"),
        ("49.144482", "15.006139", "Jindřichův Hradec", "CZ", "Europe/Prague"),
        ("48.975658", "14.480255", "České Budějovice", "CZ", "Europe/Prague"),
        ("50.511002", "14.150558", "Terezín", "CZ", "Europe/Prague"),
        ("49.183239", "15.454273", "Telč", "CZ", "Europe/Prague"),
        ("49.952431", "15.268654", "Kutná Hora", "CZ", "Europe/Prague"),
        ("49.593777", "17.250879", "Olomouc", "CZ", "Europe/Prague"),
        ("49.738430", "13.373637", "Plzeň", "CZ", "Europe/Prague"),
        ("48.812737", "14.317466", "Český Krumlov", "CZ", "Europe/Prague"),
        ("49.195061", "16.606836", "Brno", "CZ", "Europe/Prague"),
        ("50.598427", "13.610242", "Litvínov", "CZ", "Europe/Prague"),
        ("49.820923", "18.262524", "Ostrava", "CZ", "Europe/Prague"),
        ("51.04962", "12.1369", "Zeitz", "DE", "Europe/Berlin"),
        ("52.59319", "13.32127", "Wittenau", "DE", "Europe/Berlin"),
        ("50.82709", "6.9747", "Wesseling", "DE", "Europe/Berlin"),
        ("50.9803", "11.32903", "Weimar", "DE", "Europe/Berlin"),
        ("52.86147", "9.5926", "Walsrode", "DE", "Europe/Berlin"),
        ("51.88333", "8.51667", "Verl", "DE", "Europe/Berlin"),
        ("48.07667", "8.64409", "Trossingen", "DE", "Europe/Berlin"),
        ("48.78232", "9.17702", "Stuttgart", "DE", "Europe/Berlin"),
        ("53.59337", "9.47629", "Stade", "DE", "Europe/Berlin"),
        ("50.80019", "7.20769", "Siegburg", "DE", "Europe/Berlin"),
        ("51.21667", "6.26667", "Schwalmtal", "DE", "Europe/Berlin"),
        ("54.52156", "9.5586", "Schleswig", "DE", "Europe/Berlin"),
        ("50.72043", "11.34046", "Rudolstadt", "DE", "Europe/Berlin"),
        ("48.49144", "9.20427", "Reutlingen", "DE", "Europe/Berlin"),
        ("51.20219", "7.36027", "Radevormwald", "DE", "Europe/Berlin"),
        ("48.46458", "9.22796", "Pfullingen", "DE", "Europe/Berlin"),
        ("51.30001", "13.10984", "Oschatz", "DE", "Europe/Berlin"),
        ("51.47805", "6.8625", "Oberhausen", "DE", "Europe/Berlin"),
        ("50.23805", "8.86704", "Nidderau", "DE", "Europe/Berlin"),
        ("48.73218", "11.18709", "Neuburg an der Donau", "DE", "Europe/Berlin"),
        ("47.98372", "10.18527", "Memmingen", "DE", "Europe/Berlin"),
        ("50.80904", "8.77069", "Marburg an der Lahn", "DE", "Europe/Berlin"),
        ("49.5099", "6.74549", "Losheim", "DE", "Europe/Berlin"),
        ("48.52961", "12.16179", "Landshut", "DE", "Europe/Berlin"),
        ("51.19139", "6.51352", "Korschenbroich", "DE", "Europe/Berlin"),
        ("52.2", "8.63333", "Kirchlengern", "DE", "Europe/Berlin"),
        ("50.23019", "8.77155", "Karben", "DE", "Europe/Berlin"),
        ("50.09019", "8.4493", "Hofheim am Taunus", "DE", "Europe/Berlin"),
        ("52.61131", "13.31783", "Hermsdorf", "DE", "Europe/Berlin"),
        ("48.35149", "8.96317", "Hechingen", "DE", "Europe/Berlin"),
        ("53.63333", "9.85", "Halstenbek", "DE", "Europe/Berlin"),
        ("52.21099", "7.02238", "Gronau", "DE", "Europe/Berlin"),
        ("52.47774", "10.5511", "Gifhorn", "DE", "Europe/Berlin"),
        ("48.06919", "11.37703", "Gauting", "DE", "Europe/Berlin"),
        ("48.35693", "10.98461", "Friedberg", "DE", "Europe/Berlin"),
        ("51.168", "7.973", "Finnentrop", "DE", "Europe/Berlin"),
        ("49.13645", "8.91229", "Eppingen", "DE", "Europe/Berlin"),
        ("48.28259", "9.72749", "Ehingen", "DE", "Europe/Berlin"),
        ("52.4581", "13.28702", "Dahlem", "DE", "Europe/Berlin"),
        ("51.08468", "7.11393", "Burscheid", "DE", "Europe/Berlin"),
        ("49.03685", "8.70745", "Bretten", "DE", "Europe/Berlin"),
        ("49.68369", "8.61839", "Bensheim", "DE", "Europe/Berlin"),
        ("53.94313", "10.30215", "Bad Segeberg", "DE", "Europe/Berlin"),
        ("50.64336", "7.2278", "Bad Honnef", "DE", "Europe/Berlin"),
        ("49.97704", "9.15214", "Aschaffenburg", "DE", "Europe/Berlin"),
        ("48.21644", "9.02596", "Albstadt", "DE", "Europe/Berlin"),
        ("52.53048", "13.29371", "Charlottenburg-Nord", "DE", "Europe/Berlin"),
        ("53.6052", "10.03988", "Barmbek-Nord", "DE", "Europe/Berlin"),
        ("11.15583", "42.7125", "'Ali Sabieh", "DJ", "Africa/Djibouti"),
        ("55.67938", "12.53463", "Frederiksberg", "DK", "Europe/Copenhagen"),
        (
            "18.20854",
            "-71.10077",
            "Santa Cruz de Barahona",
            "DO",
            "America/Santo_Domingo",
        ),
        ("36.76639", "3.47717", "Boumerdas", "DZ", "Africa/Algiers"),
        ("36.72544", "3.55665", "Thenia", "DZ", "Africa/Algiers"),
        ("34.15429", "3.50309", "Messaad", "DZ", "Africa/Algiers"),
        ("35.21222", "2.31889", "Ksar Chellala", "DZ", "Africa/Algiers"),
        ("35.06544", "1.04945", "Frenda", "DZ", "Africa/Algiers"),
        ("36.06386", "4.62744", "El Achir", "DZ", "Africa/Algiers"),
        ("36.76775", "2.95924", "Cheraga", "DZ", "Africa/Algiers"),
        ("36.27462", "4.85668", "Bordj Zemoura", "DZ", "Africa/Algiers"),
        ("36.61954", "4.08282", "Beni Douala", "DZ", "Africa/Algiers"),
        ("-2.13404", "-79.59415", "Milagro", "EC", "America/Guayaquil"),
        ("-2.90055", "-79.00453", "Cuenca", "EC", "America/Guayaquil"),
        ("59.37722", "28.19028", "Narva", "EE", "Europe/Tallinn"),
        ("26.67319", "31.4976", "Juhaynah", "EG", "Africa/Cairo"),
        ("31.20176", "29.91582", "Alexandria", "EG", "Africa/Cairo"),
        ("39.96348", "-4.83076", "Talavera de la Reina", "ES", "Europe/Madrid"),
        ("37.35813", "-6.03731", "San Juan de Aznalfarache", "ES", "Europe/Madrid"),
        ("38.68712", "-4.10734", "Puertollano", "ES", "Europe/Madrid"),
        ("38.38479", "-0.76773", "Novelda", "ES", "Europe/Madrid"),
        ("27.76056", "-15.58602", "Maspalomas", "ES", "Atlantic/Canary"),
        ("38.47917", "-1.325", "Jumilla", "ES", "Europe/Madrid"),
        ("38.96667", "-0.18333", "Gandia", "ES", "Europe/Madrid"),
        ("38.10558", "-1.86343", "Caravaca", "ES", "Europe/Madrid"),
        ("37.49073", "-2.77259", "Baza", "ES", "Europe/Madrid"),
        ("42.64685", "-5.55835", "Villaquilambre", "ES", "Europe/Madrid"),
        ("42.06166", "-1.60452", "Tudela", "ES", "Europe/Madrid"),
        ("40.42386", "-3.53261", "San Fernando de Henares", "ES", "Europe/Madrid"),
        ("41.15612", "1.10687", "Reus", "ES", "Europe/Madrid"),
        ("41.91738", "3.1631", "Palafrugell", "ES", "Europe/Madrid"),
        ("43.32686", "-2.98884", "Leioa", "ES", "Europe/Madrid"),
        ("43.31667", "-2.68333", "Gernika-Lumo", "ES", "Europe/Madrid"),
        ("43.48961", "-8.2194", "Ferrol", "ES", "Europe/Madrid"),
        ("41.63976", "2.35739", "Cardedeu", "ES", "Europe/Madrid"),
        ("40.70995", "0.57856", "Amposta", "ES", "Europe/Madrid"),
        ("37.13548", "-3.67029", "Las Gabias", "ES", "Europe/Madrid"),
        ("42.8139", "-1.64295", "Segundo Ensanche", "ES", "Europe/Madrid"),
        ("41.41204", "2.18247", "el Camp de l'Arpa del Clot", "ES", "Europe/Madrid"),
        ("11.85", "38.01667", "Debre Tabor", "ET", "Africa/Addis_Ababa"),
        ("6.03333", "37.55", "Arba Minch", "ET", "Africa/Addis_Ababa"),
        ("65.84811", "24.14662", "Tornio", "FI", "Europe/Helsinki"),
        ("60.18427", "24.95034", "Kallio", "FI", "Europe/Helsinki"),
        ("60.2052", "24.6522", "Espoo", "FI", "Europe/Helsinki"),
        ("45.51667", "4.86667", "Vienne", "FR", "Europe/Paris"),
        ("44.92801", "4.8951", "Valence", "FR", "Europe/Paris"),
        ("44.80477", "-0.59543", "Talence", "FR", "Europe/Paris"),
        ("48.77644", "2.29026", "Sceaux", "FR", "Europe/Paris"),
        ("50.75", "2.25", "Saint-Omer", "FR", "Europe/Paris"),
        ("45.69558", "4.7934", "Saint-Genis-Laval", "FR", "Europe/Paris"),
        ("48.8765", "2.18967", "Rueil-Malmaison", "FR", "Europe/Paris"),
        ("48", "-4.1", "Quimper", "FR", "Europe/Paris"),
        ("43.11667", "1.6", "Pamiers", "FR", "Europe/Paris"),
        ("46.32313", "-0.45877", "Niort", "FR", "Europe/Paris"),
        ("43.61092", "3.87723", "Montpellier", "FR", "Europe/Paris"),
        ("48.98333", "2.61667", "Mitry-Mory", "FR", "Europe/Paris"),
        ("48.86667", "2.08333", "Marly-le-Roi", "FR", "Europe/Paris"),
        ("46.67535", "5.55575", "Lons-le-Saunier", "FR", "Europe/Paris"),
        ("43.32393", "5.4584", "Les Olives", "FR", "Europe/Paris"),
        ("48.8222", "2.12213", "Le Chesnay", "FR", "Europe/Paris"),
        ("48.90472", "2.2469", "La Garenne-Colombes", "FR", "Europe/Paris"),
        ("48.98994", "2.1699", "Herblay", "FR", "Europe/Paris"),
        ("48.98693", "2.44892", "Gonesse", "FR", "Europe/Paris"),
        ("48.79325", "2.29275", "Fontenay-aux-Roses", "FR", "Europe/Paris"),
        ("49.28669", "1.00288", "Elbeuf", "FR", "Europe/Paris"),
        ("43.71032", "-1.05366", "Dax", "FR", "Europe/Paris"),
        ("43.61058", "1.33467", "Colomiers", "FR", "Europe/Paris"),
        ("43.83125", "5.03586", "Cavaillon", "FR", "Europe/Paris"),
        ("45.73333", "4.91667", "Bron", "FR", "Europe/Paris"),
        ("48.90982", "2.45012", "Bobigny", "FR", "Europe/Paris"),
        ("48.77275", "5.16108", "Bar-le-Duc", "FR", "Europe/Paris"),
        ("43.67681", "4.63031", "Arles", "FR", "Europe/Paris"),
        ("41.91886", "8.73812", "Ajaccio", "FR", "Europe/Paris"),
        ("43.2907", "5.4384", "Marseille 11", "FR", "Europe/Paris"),
        ("-1.63333", "13.58357", "Franceville", "GA", "Africa/Libreville"),
        ("53.19146", "-2.52398", "Winsford", "GB", "Europe/London"),
        ("51.26", "-2.1875", "Westbury", "GB", "Europe/London"),
        ("51.84819", "1.26738", "Walton-on-the-Naze", "GB", "Europe/London"),
        ("52.41667", "0.75", "Thetford", "GB", "Europe/London"),
        ("51.39323", "0.47713", "Strood", "GB", "Europe/London"),
        ("50.79205", "-1.08593", "Southsea", "GB", "Europe/London"),
        ("53.78333", "-1.06667", "Selby", "GB", "Europe/London"),
        ("55.82885", "-4.21376", "Rutherglen", "GB", "Europe/London"),
        ("53.00974", "-3.05814", "Rhosllanerchrugog", "GB", "Europe/London"),
        ("53.83333", "-2.98333", "Poulton-le-Fylde", "GB", "Europe/London"),
        ("50.11861", "-5.53715", "Penzance", "GB", "Europe/London"),
        ("50.82882", "-0.32247", "Lancing", "GB", "Europe/London"),
        ("51.40148", "-1.32471", "Newbury", "GB", "Europe/London"),
        ("53.49389", "-1.29243", "Mexborough", "GB", "Europe/London"),
        ("50.75767", "-1.5443", "Lymington", "GB", "Europe/London"),
        ("53.69786", "-2.68758", "Leyland", "GB", "Europe/London"),
        ("53.7446", "-0.33525", "Kingston upon Hull", "GB", "Europe/London"),
        ("57.47908", "-4.22398", "Inverness", "GB", "Europe/London"),
        ("51.62907", "-0.74934", "High Wycombe", "GB", "Europe/London"),
        ("51.38673", "0.30367", "Hartley", "GB", "Europe/London"),
        ("52.66277", "-2.01111", "Great Wyrley", "GB", "Europe/London"),
        ("53.38333", "-0.76667", "Gainsborough", "GB", "Europe/London"),
        ("50.7236", "-3.52751", "Exeter", "GB", "Europe/London"),
        ("52.68333", "0.93333", "East Dereham", "GB", "Europe/London"),
        ("51.35084", "-1.99421", "Devizes", "GB", "Europe/London"),
        ("50.76306", "-1.29772", "Cowes", "GB", "Europe/London"),
        ("51.78967", "1.15597", "Clacton-on-Sea", "GB", "Europe/London"),
        ("53.46506", "-1.47217", "Chapletown", "GB", "Europe/London"),
        ("51.64316", "-0.36053", "Bushey", "GB", "Europe/London"),
        ("52.48173", "-2.12139", "Brierley Hill", "GB", "Europe/London"),
        ("53.81667", "-3.05", "Blackpool", "GB", "Europe/London"),
        ("53.0233", "-1.48119", "Belper", "GB", "Europe/London"),
        ("51.65", "-0.2", "Barnet", "GB", "Europe/London"),
        ("56.56317", "-2.58736", "Arbroath", "GB", "Europe/London"),
        ("57.14369", "-2.09814", "Aberdeen", "GB", "Europe/London"),
        ("51.39148", "-0.29825", "Surbiton", "GB", "Europe/London"),
        ("51.42708", "-0.91979", "Lower Earley", "GB", "Europe/London"),
        ("55.82737", "-4.0573", "Viewpark", "GB", "Europe/London"),
        ("41.82143", "41.77921", "Kobuleti", "GE", "Asia/Tbilisi"),
        ("5.30383", "-1.98956", "Tarkwa", "GH", "Africa/Accra"),
        ("7.06273", "-1.4001", "Mampong", "GH", "Africa/Accra"),
        ("6.46346", "-2.31938", "Bibiani", "GH", "Africa/Accra"),
        ("13.56667", "-15.6", "Farafenni", "GM", "Africa/Banjul"),
        ("9.535", "-13.68778", "Camayenne", "GN", "Africa/Conakry"),
        ("14.93333", "-91.11667", "Chichicastenango", "GT", "America/Guatemala"),
        ("22.37066", "114.10479", "Tsuen Wan", "HK", "Asia/Hong_Kong"),
        ("15.48131", "-86.57415", "Olanchito", "HN", "America/Tegucigalpa"),
        ("43.50891", "16.43915", "Split", "HR", "Europe/Zagreb"),
        ("18.65297", "-72.09391", "Thomazeau", "HT", "America/Port-au-Prince"),
        ("18.57677", "-72.22625", "Croix-des-Bouquets", "HT", "America/Port-au-Prince"),
        ("3.3285", "99.1625", "Tebingtinggi", "ID", "Asia/Jakarta"),
        ("3.7278", "98.6738", "Labuhan Deli", "ID", "Asia/Jakarta"),
        ("-7.51611", "109.05389", "Wangon", "ID", "Asia/Jakarta"),
        ("3.31332", "117.59152", "Tarakan", "ID", "Asia/Makassar"),
        ("-6.91806", "106.92667", "Sukabumi", "ID", "Asia/Jakarta"),
        ("-1.26424", "104.09701", "Simpang", "ID", "Asia/Jakarta"),
        ("-7.0981", "109.3243", "Randudongkal", "ID", "Asia/Jakarta"),
        ("0.51667", "101.44167", "Pekanbaru", "ID", "Asia/Jakarta"),
        ("-7.01833", "107.60389", "Pameungpeuk", "ID", "Asia/Jakarta"),
        ("-8.43333", "114.33333", "Muncar", "ID", "Asia/Jakarta"),
        ("-3.5403", "118.9707", "Majene", "ID", "Asia/Makassar"),
        ("-6.8048", "110.8405", "Kudus", "ID", "Asia/Jakarta"),
        ("-7.81667", "112.01667", "Kediri", "ID", "Asia/Jakarta"),
        ("-1.6", "103.61667", "Jambi City", "ID", "Asia/Jakarta"),
        ("-7.57897", "112.23109", "Diwek", "ID", "Asia/Jakarta"),
        ("-6.48167", "106.85417", "Cibinong", "ID", "Asia/Jakarta"),
        ("-7.73379", "113.69785", "Besuki", "ID", "Asia/Jakarta"),
        ("-1.26753", "116.82887", "Balikpapan", "ID", "Asia/Makassar"),
        ("-7.54972", "110.71639", "Ngemplak", "ID", "Asia/Jakarta"),
        ("53.53333", "-7.35", "An Muileann gCearr", "IE", "Europe/Dublin"),
        ("53.43333", "-7.95", "Athlone", "IE", "Europe/Dublin"),
        ("31.92923", "34.86563", "Ramla", "IL", "Asia/Jerusalem"),
        ("32.05971", "34.8732", "Ganei Tikva", "IL", "Asia/Jerusalem"),
        ("31.39547", "34.75699", "Rahat", "IL", "Asia/Jerusalem"),
        ("18.87813", "72.93924", "Uran", "IN", "Asia/Kolkata"),
        ("10.58806", "77.24779", "Udumalaippettai", "IN", "Asia/Kolkata"),
        ("9.82564", "78.25795", "Tiruppuvanam", "IN", "Asia/Kolkata"),
        ("25.49043", "85.94001", "Teghra", "IN", "Asia/Kolkata"),
        ("12.04161", "75.35927", "Talipparamba", "IN", "Asia/Kolkata"),
        ("26.11527", "86.59509", "Supaul", "IN", "Asia/Kolkata"),
        ("34.08565", "74.80555", "Srinagar", "IN", "Asia/Kolkata"),
        ("25.92493", "73.66633", "Sojat", "IN", "Asia/Kolkata"),
        ("14.62072", "74.83554", "Sirsi", "IN", "Asia/Kolkata"),
        ("25.13915", "73.06784", "Sheoganj", "IN", "Asia/Kolkata"),
        ("11.50526", "77.23826", "Sathyamangalam", "IN", "Asia/Kolkata"),
        ("21.46527", "83.97573", "Sambalpur", "IN", "Asia/Kolkata"),
        ("25.87498", "86.59611", "Saharsa", "IN", "Asia/Kolkata"),
        ("12.95629", "78.27539", "Robertsonpet", "IN", "Asia/Kolkata"),
        ("26.44931", "91.61356", "Rangia", "IN", "Asia/Kolkata"),
        ("33.37526", "74.3092", "Rajaori", "IN", "Asia/Kolkata"),
        ("24.81757", "84.63445", "Rafiganj", "IN", "Asia/Kolkata"),
        ("18.51957", "73.85535", "Pune", "IN", "Asia/Kolkata"),
        ("11.93381", "79.82979", "Puducherry", "IN", "Asia/Kolkata"),
        ("28.71271", "77.656", "Pilkhua", "IN", "Asia/Kolkata"),
        ("10.12268", "77.54372", "Periyakulam", "IN", "Asia/Kolkata"),
        ("31.28092", "74.85849", "Patti", "IN", "Asia/Kolkata"),
        ("20.88098", "75.11937", "Parola", "IN", "Asia/Kolkata"),
        ("23.07492", "88.28637", "Pandua", "IN", "Asia/Kolkata"),
        ("18.18158", "76.03889", "Osmanabad", "IN", "Asia/Kolkata"),
        ("25.6439", "77.9129", "Narwar", "IN", "Asia/Kolkata"),
        ("30.81383", "75.16878", "Moga", "IN", "Asia/Kolkata"),
        ("28.98002", "77.70636", "Meerut", "IN", "Asia/Kolkata"),
        ("11.12018", "76.11996", "Manjeri", "IN", "Asia/Kolkata"),
        ("30.21121", "74.4818", "Malaut", "IN", "Asia/Kolkata"),
        ("25.92127", "86.79271", "Madhipura", "IN", "Asia/Kolkata"),
        ("24.05979", "77.40858", "Leteri", "IN", "Asia/Kolkata"),
        ("21.34222", "71.30633", "Kundla", "IN", "Asia/Kolkata"),
        ("22.75218", "72.68533", "Kheda", "IN", "Asia/Kolkata"),
        ("23.1959", "86.51499", "Kenda", "IN", "Asia/Kolkata"),
        ("29.21399", "78.95693", "Kashipur", "IN", "Asia/Kolkata"),
        ("11.00599", "77.5609", "Kangayam", "IN", "Asia/Kolkata"),
        ("22.88783", "84.13864", "Jashpurnagar", "IN", "Asia/Kolkata"),
        ("26.2649", "81.54855", "Jais", "IN", "Asia/Kolkata"),
        ("16.06213", "76.0586", "Hungund", "IN", "Asia/Kolkata"),
        ("29.22254", "79.5286", "Haldwani", "IN", "Asia/Kolkata"),
        ("26.76628", "83.36889", "Gorakhpur", "IN", "Asia/Kolkata"),
        ("12.25282", "79.41727", "Gingee", "IN", "Asia/Kolkata"),
        ("21.53889", "71.57737", "Gariadhar", "IN", "Asia/Kolkata"),
        ("15.73628", "75.96976", "Gajendragarh", "IN", "Asia/Kolkata"),
        ("17.54907", "82.85749", "Elamanchili", "IN", "Asia/Kolkata"),
        ("19.21667", "73.08333", "Dombivli", "IN", "Asia/Kolkata"),
        ("22.19303", "88.18466", "Diamond Harbour", "IN", "Asia/Kolkata"),
        ("12.1277", "78.15794", "Dharmapuri", "IN", "Asia/Kolkata"),
        ("25.75728", "75.37991", "Deoli", "IN", "Asia/Kolkata"),
        ("14.46693", "75.92694", "Davangere", "IN", "Asia/Kolkata"),
        ("25.66795", "85.83636", "Dalsingh Sarai", "IN", "Asia/Kolkata"),
        ("15.5439", "73.7553", "Calangute", "IN", "Asia/Kolkata"),
        ("27.9247", "78.40102", "Chharra", "IN", "Asia/Kolkata"),
        ("32.55531", "76.12647", "Chamba", "IN", "Asia/Kolkata"),
        ("20.88197", "85.83334", "Bhuban", "IN", "Asia/Kolkata"),
        ("19.30157", "72.85107", "Bhayandar", "IN", "Asia/Kolkata"),
        ("15.45144", "78.14797", "Betamcherla", "IN", "Asia/Kolkata"),
        ("26.32293", "91.00632", "Barpeta", "IN", "Asia/Kolkata"),
        ("28.92694", "78.23456", "Bachhraon", "IN", "Asia/Kolkata"),
        ("21.59983", "71.21169", "Amreli", "IN", "Asia/Kolkata"),
        ("10.10649", "76.35484", "Alwaye", "IN", "Asia/Kolkata"),
        ("24.41288", "76.56719", "Aklera", "IN", "Asia/Kolkata"),
        ("23.49668", "86.68363", "Adra", "IN", "Asia/Kolkata"),
        ("22.4711", "88.1453", "Pujali", "IN", "Asia/Kolkata"),
        ("22.10194", "85.37752", "Barbil", "IN", "Asia/Kolkata"),
        ("17.34769", "78.55757", "Lal Bahadur Nagar", "IN", "Asia/Kolkata"),
        ("23.18", "88.58", "Aistala", "IN", "Asia/Kolkata"),
        ("9.57046", "76.32756", "Kalavoor", "IN", "Asia/Kolkata"),
        ("32.61603", "44.02488", "Karbala", "IQ", "Asia/Baghdad"),
        ("35.6803", "51.0193", "Shahre Jadide Andisheh", "IR", "Asia/Tehran"),
        ("36.64852", "51.49621", "Nowshahr", "IR", "Asia/Tehran"),
        ("33.14447", "47.3799", "Darreh Shahr", "IR", "Asia/Tehran"),
        ("33.86419", "48.26258", "Aleshtar", "IR", "Asia/Tehran"),
        ("32.65246", "51.67462", "Isfahan", "IR", "Asia/Tehran"),
        ("38.07789", "13.44275", "Villabate", "IT", "Europe/Rome"),
        ("36.92574", "14.72443", "Ragusa", "IT", "Europe/Rome"),
        ("37.51803", "15.00913", "Misterbianco", "IT", "Europe/Rome"),
        ("37.49223", "15.07041", "Catania", "IT", "Europe/Rome"),
        ("37.31065", "13.57661", "Agrigento", "IT", "Europe/Rome"),
        ("43.78956", "7.60872", "Ventimiglia", "IT", "Europe/Rome"),
        ("44.89784", "8.86374", "Tortona", "IT", "Europe/Rome"),
        ("40.87329", "14.43865", "Somma Vesuviana", "IT", "Europe/Rome"),
        ("40.72586", "8.55552", "Sassari", "IT", "Europe/Rome"),
        ("45.39402", "9.29109", "San Giuliano Milanese", "IT", "Europe/Rome"),
        ("42.67164", "14.01481", "Roseto degli Abruzzi", "IT", "Europe/Rome"),
        ("45.78071", "12.84052", "Portogruaro", "IT", "Europe/Rome"),
        ("43.1122", "12.38878", "Perugia", "IT", "Europe/Rome"),
        ("45.44694", "8.62118", "Novara", "IT", "Europe/Rome"),
        ("45.50369", "11.412", "Montecchio Maggiore-Alte Ceccato", "IT", "Europe/Rome"),
        ("40.55851", "17.80774", "Mesagne", "IT", "Europe/Rome"),
        ("45.79377", "8.88104", "Malnate", "IT", "Europe/Rome"),
        ("42.22718", "14.39024", "Lanciano", "IT", "Europe/Rome"),
        ("45.53069", "9.40531", "Gorgonzola", "IT", "Europe/Rome"),
        ("40.53123", "17.58522", "Francavilla Fontana", "IT", "Europe/Rome"),
        ("43.62558", "13.39954", "Falconara Marittima", "IT", "Europe/Rome"),
        ("45.9836", "12.70038", "Cordenons", "IT", "Europe/Rome"),
        ("44.31771", "9.32241", "Chiavari", "IT", "Europe/Rome"),
        ("44.59445", "11.04979", "Castelfranco Emilia", "IT", "Europe/Rome"),
        ("41.55947", "14.66737", "Campobasso", "IT", "Europe/Rome"),
        ("41.24264", "16.50104", "Bisceglie", "IT", "Europe/Rome"),
        ("41.72063", "12.6723", "Ariccia", "IT", "Europe/Rome"),
        ("40.92298", "14.30935", "Afragola", "IT", "Europe/Rome"),
        ("40.87363", "14.34085", "Volla", "IT", "Europe/Rome"),
        ("18.00747", "-76.78319", "New Kingston", "JM", "America/Jamaica"),
        ("35.8", "137.23333", "Gero", "JP", "Asia/Tokyo"),
        ("34.61667", "135.6", "Yao", "JP", "Asia/Tokyo"),
        ("34.75856", "136.13108", "Ueno-ebisumachi", "JP", "Asia/Tokyo"),
        ("34.81667", "137.4", "Toyokawa", "JP", "Asia/Tokyo"),
        ("34.4833", "136.84186", "Toba", "JP", "Asia/Tokyo"),
        ("36.65", "138.31667", "Suzaka", "JP", "Asia/Tokyo"),
        ("34.9", "137.5", "Shinshiro", "JP", "Asia/Tokyo"),
        ("35.06667", "135.21667", "Sasayama", "JP", "Asia/Tokyo"),
        ("36", "139.55722", "Okegawa", "JP", "Asia/Tokyo"),
        ("36.53333", "136.61667", "Nonoichi", "JP", "Asia/Tokyo"),
        ("36.75965", "137.36215", "Namerikawa", "JP", "Asia/Tokyo"),
        ("35", "136.51667", "Komono", "JP", "Asia/Tokyo"),
        ("33.4425", "129.96972", "Karatsu", "JP", "Asia/Tokyo"),
        ("35.30889", "139.55028", "Kamakura", "JP", "Asia/Tokyo"),
        ("34.25", "135.31667", "Iwade", "JP", "Asia/Tokyo"),
        ("35.82756", "137.95378", "Ina", "JP", "Asia/Tokyo"),
        ("33.3213", "130.94098", "Hita", "JP", "Asia/Tokyo"),
        ("36.24624", "139.07204", "Fujioka", "JP", "Asia/Tokyo"),
        ("36.33011", "138.89585", "Annaka", "JP", "Asia/Tokyo"),
        ("35.815", "139.6853", "Shimotoda", "JP", "Asia/Tokyo"),
        ("39.46667", "141.95", "Yamada", "JP", "Asia/Tokyo"),
        ("37.56667", "140.11667", "Inawashiro", "JP", "Asia/Tokyo"),
        ("43.82634", "144.09638", "Motomachi", "JP", "Asia/Tokyo"),
        ("44.35056", "142.45778", "Nayoro", "JP", "Asia/Tokyo"),
        ("41.77583", "140.73667", "Hakodate", "JP", "Asia/Tokyo"),
        ("35.48199", "137.02166", "Minokamo", "JP", "Asia/Tokyo"),
        ("0.03813", "36.36339", "Nyahururu", "KE", "Africa/Nairobi"),
        ("3.11988", "35.59642", "Lodwar", "KE", "Africa/Nairobi"),
        ("0.46005", "34.11169", "Busia", "KE", "Africa/Nairobi"),
        ("40.93333", "73", "Jalal-Abad", "KG", "Asia/Bishkek"),
        ("13.65805", "102.56365", "Paoy Paet", "KH", "Asia/Phnom_Penh"),
        ("36.82167", "128.63083", "Eisen", "KR", "Asia/Seoul"),
        ("37.1759", "128.9889", "T‚Äôaebaek", "KR", "Asia/Seoul"),
        ("36.20389", "127.08472", "Nonsan", "KR", "Asia/Seoul"),
        ("37.65639", "126.835", "Goyang-si", "KR", "Asia/Seoul"),
        ("36.6009", "126.665", "Hongseong", "KR", "Asia/Seoul"),
        ("34.8825", "128.62667", "Sinhyeon", "KR", "Asia/Seoul"),
        ("47.83333", "59.6", "Shalqar", "KZ", "Asia/Aqtobe"),
        ("47.46657", "84.87144", "Zaysan", "KZ", "Asia/Almaty"),
        ("44.85278", "65.50917", "Kyzylorda", "KZ", "Asia/Qyzylorda"),
        ("43.41949", "77.0202", "Otegen Batyra", "KZ", "Asia/Almaty"),
        ("6.84019", "79.87116", "Dehiwala-Mount Lavinia", "LK", "Asia/Colombo"),
        ("6.9909", "79.883", "Hendala", "LK", "Asia/Colombo"),
        ("7.57944", "-8.53778", "New Yekepa", "LR", "Africa/Monrovia"),
        ("55.25", "24.75", "Ukmerge", "LT", "Europe/Vilnius"),
        ("54.39635", "24.04142", "Alytus", "LT", "Europe/Vilnius"),
        ("30.75545", "20.22625", "Ajdabiya", "LY", "Africa/Tripoli"),
        ("24.96334", "10.18003", "Ghat", "LY", "Africa/Tripoli"),
        ("33.92866", "-6.90656", "Temara", "MA", "Africa/Casablanca"),
        ("33.42585", "-6.00137", "Oulmes", "MA", "Africa/Casablanca"),
        ("34.31", "-2.16", "Jerada", "MA", "Africa/Casablanca"),
        ("33.43443", "-5.22126", "Azrou", "MA", "Africa/Casablanca"),
        ("48.15659", "28.28489", "Soroca", "MD", "Europe/Chisinau"),
        ("42.28639", "18.84", "Budva", "ME", "Europe/Podgorica"),
        ("-22.9", "44.53333", "Sakaraha", "MG", "Indian/Antananarivo"),
        ("-21.15", "46.58333", "Ikalamavony", "MG", "Indian/Antananarivo"),
        ("-19.65", "47.31667", "Antanifotsy", "MG", "Indian/Antananarivo"),
        ("-17.83333", "48.41667", "Ambatondrazaka", "MG", "Indian/Antananarivo"),
        ("42", "21.32778", "Saraj", "MK", "Europe/Skopje"),
        ("41.92361", "20.91361", "Bogovinje", "MK", "Europe/Skopje"),
        ("12.74409", "-8.07257", "Kati", "ML", "Africa/Bamako"),
        ("14.0823", "98.19151", "Dawei", "MM", "Asia/Yangon"),
        ("16.68911", "98.50893", "Myawadi", "MM", "Asia/Yangon"),
        ("17.30858", "97.01124", "Kyaikto", "MM", "Asia/Yangon"),
        ("47.90771", "106.88324", "Ulan Bator", "MN", "Asia/Ulaanbaatar"),
        ("14.67751", "-60.94228", "Le Robert", "MQ", "America/Martinique"),
        ("35.89972", "14.51472", "Valletta", "MT", "Europe/Malta"),
        ("-13.7804", "34.4587", "Salima", "MW", "Africa/Blantyre"),
        ("16.75973", "-93.11308", "Tuxtla", "MX", "America/Mexico_City"),
        ("19.8173", "-97.35992", "Teziutlan", "MX", "America/Mexico_City"),
        ("21.28306", "-89.66123", "Progreso", "MX", "America/Merida"),
        ("17.06542", "-96.72365", "Oaxaca", "MX", "America/Mexico_City"),
        ("25.87972", "-97.50417", "Heroica Matamoros", "MX", "America/Matamoros"),
        ("19.32932", "-98.1664", "Contla", "MX", "America/Mexico_City"),
        ("17.94979", "-94.91386", "Acayucan", "MX", "America/Mexico_City"),
        ("19.32889", "-99.32556", "San Lorenzo Acopilco", "MX", "America/Mexico_City"),
        ("20.22816", "-103.5687", "Zacoalco de Torres", "MX", "America/Mexico_City"),
        ("20.74122", "-100.44843", "Santa Rosa Jauregui", "MX", "America/Mexico_City"),
        ("20.21322", "-100.88023", "Salvatierra", "MX", "America/Mexico_City"),
        ("19.64745", "-102.04897", "Paracho de Verduzco", "MX", "America/Mexico_City"),
        ("20.28527", "-103.42897", "Jocotepec", "MX", "America/Mexico_City"),
        ("21.01858", "-101.2591", "Guanajuato", "MX", "America/Mexico_City"),
        ("22.49396", "-105.36369", "Acaponeta", "MX", "America/Mazatlan"),
        ("19.04222", "-98.11889", "Casa Blanca", "MX", "America/Mexico_City"),
        ("1.6561", "103.6032", "Kulai", "MY", "Asia/Kuala_Lumpur"),
        ("5.90702", "116.10146", "Donggongon", "MY", "Asia/Kuching"),
        ("4.88441", "101.96857", "Gua Musang", "MY", "Asia/Kuala_Lumpur"),
        ("5.4709", "100.24529", "Batu Feringgi", "MY", "Asia/Kuala_Lumpur"),
        ("4.02219", "101.02083", "Teluk Intan", "MY", "Asia/Kuala_Lumpur"),
        ("1.6", "103.81667", "Ulu Tiram", "MY", "Asia/Kuala_Lumpur"),
        ("2.2139", "102.3278", "Kampung Ayer Molek", "MY", "Asia/Kuala_Lumpur"),
        ("-23.85972", "35.34722", "Maxixe", "MZ", "Africa/Maputo"),
        ("-21.98333", "16.91667", "Okahandja", "NA", "Africa/Windhoek"),
        ("13.70727", "9.15013", "Mirriah", "NE", "Africa/Niamey"),
        ("4.92675", "6.26764", "Yenagoa", "NG", "Africa/Lagos"),
        ("6.8485", "3.64633", "Shagamu", "NG", "Africa/Lagos"),
        ("7.6", "4.18333", "Olupona", "NG", "Africa/Lagos"),
        ("6.15038", "6.83042", "Nkpor", "NG", "Africa/Lagos"),
        ("6.45407", "3.39467", "Lagos", "NG", "Africa/Lagos"),
        ("9.58126", "8.2926", "Kafanchan", "NG", "Africa/Lagos"),
        ("7.62789", "4.74161", "Ilesa", "NG", "Africa/Lagos"),
        ("7.50251", "5.06258", "Igbara-Odo", "NG", "Africa/Lagos"),
        ("11.86064", "9.0027", "Gaya", "NG", "Africa/Lagos"),
        ("7.65649", "4.92235", "Efon-Alaaye", "NG", "Africa/Lagos"),
        ("10.61285", "12.19458", "Biu", "NG", "Africa/Lagos"),
        ("12.74482", "4.52514", "Argungu", "NG", "Africa/Lagos"),
        ("13.48082", "-86.58208", "Somoto", "NI", "America/Managua"),
        ("11.84962", "-86.19903", "Jinotepe", "NI", "America/Managua"),
        ("52.09", "5.23333", "Zeist", "NL", "Europe/Amsterdam"),
        ("51.65333", "5.2875", "Vught", "NL", "Europe/Amsterdam"),
        ("51.44889", "5.51978", "Tongelre", "NL", "Europe/Amsterdam"),
        ("51.95838", "4.47124", "Schiebroek", "NL", "Europe/Amsterdam"),
        ("52.31333", "6.92917", "Oldenzaal", "NL", "Europe/Amsterdam"),
        ("52.26083", "7.00417", "Losser", "NL", "Europe/Amsterdam"),
        ("53.16167", "6.76111", "Hoogezand", "NL", "Europe/Amsterdam"),
        ("52.57583", "6.61944", "Hardenberg", "NL", "Europe/Amsterdam"),
        ("52.71083", "5.74861", "Emmeloord", "NL", "Europe/Amsterdam"),
        ("51.955", "5.22778", "Culemborg", "NL", "Europe/Amsterdam"),
        ("52.14", "5.58472", "Barneveld", "NL", "Europe/Amsterdam"),
        ("68.79833", "16.54165", "Harstad", "NO", "Europe/Oslo"),
        ("-44.39672", "171.25364", "Timaru", "NZ", "Pacific/Auckland"),
        ("-38.65333", "178.00417", "Gisborne", "NZ", "Pacific/Auckland"),
        ("8.88988", "-79.62603", "Veracruz", "PA", "America/Panama"),
        ("9.15093", "-79.62098", "Chilibre", "PA", "America/Panama"),
        ("-3.74912", "-73.25383", "Iquitos", "PE", "America/Lima"),
        ("-16.25", "-69.08333", "Yunguyo", "PE", "America/Lima"),
        ("-15.21194", "-75.11028", "Minas de Marcona", "PE", "America/Lima"),
        ("-11.94306", "-76.70944", "Chosica", "PE", "America/Lima"),
        ("-5.85746", "144.23058", "Mount Hagen", "PG", "Pacific/Port_Moresby"),
        ("6.33444", "124.95278", "Tupi", "PH", "Asia/Manila"),
        ("10.7375", "122.9666", "Talisay", "PH", "Asia/Manila"),
        ("12.97389", "123.99333", "Sorsogon", "PH", "Asia/Manila"),
        ("9.3337", "122.8637", "Santa Catalina", "PH", "Asia/Manila"),
        ("12.35275", "121.06761", "San Jose", "PH", "Asia/Manila"),
        ("6.95194", "121.96361", "Recodo", "PH", "Asia/Manila"),
        ("14.66", "120.56528", "Pilar", "PH", "Asia/Manila"),
        ("10.20898", "123.758", "Naga", "PH", "Asia/Manila"),
        ("12.37169", "123.62494", "Masbate", "PH", "Asia/Manila"),
        ("16.0438", "120.4861", "Manaoag", "PH", "Asia/Manila"),
        ("10.13361", "124.84472", "Maasin", "PH", "Asia/Manila"),
        ("16.455", "120.5875", "La Trinidad", "PH", "Asia/Manila"),
        ("9.6531", "124.3697", "Jagna", "PH", "Asia/Manila"),
        ("14.8361", "120.97844", "Guyong", "PH", "Asia/Manila"),
        ("8.56697", "123.33471", "Dipolog", "PH", "Asia/Manila"),
        ("10.31672", "123.89071", "Cebu City", "PH", "Asia/Manila"),
        ("14.14989", "121.3152", "Calauan", "PH", "Asia/Manila"),
        ("15.72892", "120.57224", "Burgos", "PH", "Asia/Manila"),
        ("14.95472", "120.89694", "Baliuag", "PH", "Asia/Manila"),
        ("14.62578", "121.12251", "Antipolo", "PH", "Asia/Manila"),
        ("27.52948", "68.75915", "Khairpur Mir‚Äôs", "PK", "Asia/Karachi"),
        ("26.9423", "68.11759", "Tharu Shah", "PK", "Asia/Karachi"),
        ("31.82539", "72.54064", "Sillanwali", "PK", "Asia/Karachi"),
        ("31.71667", "73.38333", "Sangla Hill", "PK", "Asia/Karachi"),
        ("30.29184", "71.67164", "Qadirpur Ran", "PK", "Asia/Karachi"),
        ("31.96258", "73.97117", "Naushahra Virkan", "PK", "Asia/Karachi"),
        ("32.57756", "71.52847", "Mianwali", "PK", "Asia/Karachi"),
        ("27.55898", "68.21204", "Larkana", "PK", "Asia/Karachi"),
        ("30.46907", "70.96699", "Kot Addu", "PK", "Asia/Karachi"),
        ("30.76468", "74.12286", "Kanganpur", "PK", "Asia/Karachi"),
        ("25.95533", "68.88871", "Jhol", "PK", "Asia/Karachi"),
        ("29.69221", "72.54566", "Hasilpur", "PK", "Asia/Karachi"),
        ("32.17629", "75.06583", "Fazilpur", "PK", "Asia/Karachi"),
        ("32.87533", "71.57118", "Daud Khel", "PK", "Asia/Karachi"),
        ("25.80565", "68.49143", "Bhit Shah", "PK", "Asia/Karachi"),
        ("29.38242", "70.91106", "Alipur", "PK", "Asia/Karachi"),
        ("51.14942", "15.00835", "Zgorzelec", "PL", "Europe/Warsaw"),
        ("54.58048", "16.86194", "Ustka", "PL", "Europe/Warsaw"),
        ("50.5107", "18.30056", "Strzelce Opolskie", "PL", "Europe/Warsaw"),
        ("54.60528", "18.34717", "Reda", "PL", "Europe/Warsaw"),
        ("50.20528", "19.27498", "Jaworzno", "PL", "Europe/Warsaw"),
        ("50.86079", "17.4674", "Brzeg", "PL", "Europe/Warsaw"),
        ("18.42745", "-67.15407", "Aguadilla", "PR", "America/Puerto_Rico"),
        ("18.03496", "-66.8499", "Yauco", "PR", "America/Puerto_Rico"),
        ("31.78336", "35.23388", "East Jerusalem", "PS", "Asia/Hebron"),
        ("38.72706", "-9.24671", "Carnaxide", "PT", "Europe/Lisbon"),
        ("37.08819", "-8.2503", "Albufeira", "PT", "Europe/Lisbon"),
        ("41.20485", "-8.33147", "Paredes", "PT", "Europe/Lisbon"),
        ("41.1053", "-7.32097", "Custoias", "PT", "Europe/Lisbon"),
        ("37.74615", "-25.66689", "Ponta Delgada", "PT", "Atlantic/Azores"),
        ("-20.88231", "55.4504", "Saint-Denis", "RE", "Indian/Reunion"),
        ("44.43579", "26.01649", "Sector 6", "RO", "Europe/Bucharest"),
        ("44.22639", "22.53083", "Negotin", "RS", "Europe/Belgrade"),
        ("44.97639", "19.61222", "Sremska Mitrovica", "RS", "Europe/Belgrade"),
        ("53.53395", "33.72798", "Zhukovka", "RU", "Europe/Moscow"),
        ("46.7055", "38.2739", "Yeysk", "RU", "Europe/Moscow"),
        ("44.98901", "38.94324", "Yablonovskiy", "RU", "Europe/Moscow"),
        ("56.03361", "35.96944", "Volokolamsk", "RU", "Europe/Moscow"),
        ("57.97472", "33.2525", "Valday", "RU", "Europe/Moscow"),
        ("56.85836", "35.90057", "Tver", "RU", "Europe/Moscow"),
        ("55.62047", "37.49338", "Tyoply Stan", "RU", "Europe/Moscow"),
        ("54.90083", "38.07083", "Stupino", "RU", "Europe/Moscow"),
        ("55.63711", "37.38115", "Solntsevo", "RU", "Europe/Moscow"),
        ("59.80917", "30.38167", "Shushary", "RU", "Europe/Moscow"),
        ("64.5635", "39.8302", "Severodvinsk", "RU", "Europe/Moscow"),
        ("51.78771", "56.36091", "Saraktash", "RU", "Asia/Yekaterinburg"),
        ("53.95278", "32.86389", "Roslavl‚Äô", "RU", "Europe/Moscow"),
        ("51.40944", "46.04833", "Privolzhskiy", "RU", "Europe/Saratov"),
        ("61.78491", "34.34691", "Petrozavodsk", "RU", "Europe/Moscow"),
        ("53.37596", "51.3452", "Otradnyy", "RU", "Europe/Samara"),
        ("54.48147", "53.47103", "Oktyabr‚Äôskiy", "RU", "Asia/Yekaterinburg"),
        ("43.96222", "43.63417", "Novopavlovsk", "RU", "Europe/Moscow"),
        ("53.53041", "43.67663", "Nizhniy Lomov", "RU", "Europe/Moscow"),
        ("55.38752", "36.73307", "Naro-Fominsk", "RU", "Europe/Moscow"),
        ("50.06", "43.2379", "Mikhaylovka", "RU", "Europe/Volgograd"),
        ("55.64776", "38.02486", "Malakhovka", "RU", "Europe/Moscow"),
        ("55.85", "37.56667", "Likhobory", "RU", "Europe/Moscow"),
        ("51.4781", "57.3552", "Kuvandyk", "RU", "Asia/Yekaterinburg"),
        ("44.92934", "37.99117", "Krymsk", "RU", "Europe/Moscow"),
        ("54.03876", "43.91385", "Kovylkino", "RU", "Europe/Moscow"),
        ("60.02427", "30.28491", "Kolomyagi", "RU", "Europe/Moscow"),
        ("53.93361", "37.92792", "Kireyevsk", "RU", "Europe/Moscow"),
        ("54.84444", "38.16694", "Kashira", "RU", "Europe/Moscow"),
        ("58.7002", "59.4839", "Kachkanar", "RU", "Asia/Yekaterinburg"),
        ("43.35071", "46.10925", "Gudermes", "RU", "Europe/Moscow"),
        ("57.30185", "39.85331", "Gavrilov-Yam", "RU", "Europe/Moscow"),
        ("53.59782", "34.33825", "Dyat‚Äôkovo", "RU", "Europe/Moscow"),
        ("58.1908", "40.17171", "Danilov", "RU", "Europe/Moscow"),
        ("42.819", "47.1192", "Buynaksk", "RU", "Europe/Moscow"),
        ("53.77166", "38.12408", "Bogoroditsk", "RU", "Europe/Moscow"),
        ("54.39304", "53.26023", "Bavly", "RU", "Europe/Moscow"),
        ("55.39485", "43.83992", "Arzamas", "RU", "Europe/Moscow"),
        ("54.8421", "46.5813", "Alatyr‚Äô", "RU", "Europe/Moscow"),
        ("58.63667", "59.80222", "Lesnoy", "RU", "Asia/Yekaterinburg"),
        ("55.8736", "85.4265", "Yashkino", "RU", "Asia/Novokuznetsk"),
        ("58.04254", "65.27258", "Tavda", "RU", "Asia/Yekaterinburg"),
        ("55.54028", "89.20083", "Sharypovo", "RU", "Asia/Krasnoyarsk"),
        ("53.30972", "83.62389", "Novosilikatnyy", "RU", "Asia/Barnaul"),
        ("58.23583", "92.48278", "Lesosibirsk", "RU", "Asia/Krasnoyarsk"),
        ("56.11281", "69.49015", "Ishim", "RU", "Asia/Yekaterinburg"),
        ("56.9083", "60.8019", "Beryozovsky", "RU", "Asia/Yekaterinburg"),
        ("55.75556", "60.70278", "Ozersk", "RU", "Asia/Yekaterinburg"),
        ("51.82721", "107.60627", "Ulan-Ude", "RU", "Asia/Irkutsk"),
        ("45.47885", "133.42825", "Lesozavodsk", "RU", "Asia/Vladivostok"),
        ("65.93381", "111.4834", "Aykhal", "RU", "Asia/Yakutsk"),
        ("53.14657", "140.72287", "Nikolayevsk-on-Amure", "RU", "Asia/Vladivostok"),
        ("60.97944", "76.92421", "Izluchinsk", "RU", "Asia/Yekaterinburg"),
        ("-1.9487", "30.4347", "Rwamagana", "RW", "Africa/Kigali"),
        ("27.0174", "49.62251", "Al Jubayl", "SA", "Asia/Riyadh"),
        ("11.8659", "34.3869", "Ar Ruseris", "SD", "Africa/Khartoum"),
        ("61.72744", "17.10558", "Hudiksvall", "SE", "Europe/Stockholm"),
        ("59.33333", "18.28333", "Boo", "SE", "Europe/Stockholm"),
        ("48.8449", "17.22635", "Skalica", "SK", "Europe/Bratislava"),
        ("48.43174", "17.8031", "Hlohovec", "SK", "Europe/Bratislava"),
        ("8.48714", "-13.2356", "Freetown", "SL", "Africa/Freetown"),
        ("-0.35817", "42.54536", "Kismayo", "SO", "Africa/Mogadishu"),
        ("9.89206", "43.38531", "Baki", "SO", "Africa/Mogadishu"),
        ("13.73417", "-89.71472", "Sonzacate", "SV", "America/El_Salvador"),
        ("13.70167", "-89.10944", "Ilopango", "SV", "America/El_Salvador"),
        ("34.5624", "38.28402", "Tadmur", "SY", "Asia/Damascus"),
        ("35.95664", "36.7138", "Binnish", "SY", "Asia/Damascus"),
        ("12.18441", "18.69303", "Mongo", "TD", "Africa/Ndjamena"),
        ("15.46063", "99.89166", "Thap Than", "TH", "Asia/Bangkok"),
        ("8.43333", "99.96667", "Nakhon Si Thammarat", "TH", "Asia/Bangkok"),
        ("13.51825", "99.95469", "Damnoen Saduak", "TH", "Asia/Bangkok"),
        ("15.79408", "104.1451", "Yasothon", "TH", "Asia/Bangkok"),
        ("6.25947", "102.05461", "Tak Bai", "TH", "Asia/Bangkok"),
        ("16.0567", "103.65309", "Roi Et", "TH", "Asia/Bangkok"),
        ("13.44581", "101.18445", "Phanat Nikhom", "TH", "Asia/Bangkok"),
        ("13.8196", "100.04427", "Nakhon Pathom", "TH", "Asia/Bangkok"),
        ("14.64056", "104.64992", "Kantharalak", "TH", "Asia/Bangkok"),
        ("15.58552", "102.42587", "Bua Yai", "TH", "Asia/Bangkok"),
        ("14.37395", "100.48528", "Bang Ban", "TH", "Asia/Bangkok"),
        ("38.55632", "69.01354", "Vahdat", "TJ", "Asia/Dushanbe"),
        ("-8.99167", "125.21972", "Maliana", "TL", "Asia/Dili"),
        ("36.08497", "9.37082", "Siliana", "TN", "Africa/Tunis"),
        ("35.72917", "10.58082", "Msaken", "TN", "Africa/Tunis"),
        ("36.46917", "10.78222", "Beni Khiar", "TN", "Africa/Tunis"),
        ("37.16911", "10.03478", "El Alia", "TN", "Africa/Tunis"),
        ("38.13708", "41.00817", "Silvan", "TR", "Europe/Istanbul"),
        ("39.22493", "42.85693", "Patnos", "TR", "Europe/Istanbul"),
        ("37.31309", "40.74357", "Mardin", "TR", "Europe/Istanbul"),
        ("37.58105", "29.26639", "Serinhisar", "TR", "Europe/Istanbul"),
        ("37.05944", "37.3825", "Gaziantep", "TR", "Europe/Istanbul"),
        ("39.59611", "27.02444", "Edremit", "TR", "Europe/Istanbul"),
        ("39.12074", "27.18052", "Bergama", "TR", "Europe/Istanbul"),
        ("38.37255", "34.02537", "Aksaray", "TR", "Europe/Istanbul"),
        ("40.98894", "28.67582", "Yakuplu", "TR", "Europe/Istanbul"),
        ("40.1675", "34.37389", "Sungurlu", "TR", "Europe/Istanbul"),
        ("40.37528", "28.88222", "Mudanya", "TR", "Europe/Istanbul"),
        ("10.66668", "-61.51889", "Port of Spain", "TT", "America/Port_of_Spain"),
        ("23.5654", "119.58627", "Magong", "TW", "Asia/Taipei"),
        ("-2.68333", "33", "Usagara", "TZ", "Africa/Dar_es_Salaam"),
        ("-4.06667", "37.73333", "Same", "TZ", "Africa/Dar_es_Salaam"),
        ("-6.25", "38.66667", "Mvomero", "TZ", "Africa/Dar_es_Salaam"),
        ("-4.83", "29.65806", "Mwandiga", "TZ", "Africa/Dar_es_Salaam"),
        ("-6.8", "39.25", "Magomeni", "TZ", "Africa/Dar_es_Salaam"),
        ("-7.60361", "37.00438", "Kidodi", "TZ", "Africa/Dar_es_Salaam"),
        ("-7.76667", "35.7", "Iringa", "TZ", "Africa/Dar_es_Salaam"),
        ("-5.41667", "38.01667", "Chanika", "TZ", "Africa/Dar_es_Salaam"),
        ("-10.33333", "39.28333", "Nyangao", "TZ", "Africa/Dar_es_Salaam"),
        ("49.07866", "30.96755", "Zvenihorodka", "UA", "Europe/Kiev"),
        ("47.56494", "31.33078", "Voznesensk", "UA", "Europe/Kiev"),
        ("49.41029", "38.15035", "Svatove", "UA", "Europe/Zaporozhye"),
        ("50.18545", "27.06365", "Shepetivka", "UA", "Europe/Kiev"),
        ("47.48444", "36.25361", "Polohy", "UA", "Europe/Zaporozhye"),
        ("46.75451", "33.34864", "Nova Kakhovka", "UA", "Europe/Kiev"),
        ("50.75932", "25.34244", "Lutsk", "UA", "Europe/Kiev"),
        ("49.65186", "26.97253", "Krasyliv", "UA", "Europe/Kiev"),
        ("46.65581", "32.6178", "Kherson", "UA", "Europe/Kiev"),
        ("51.67822", "33.9162", "Hlukhiv", "UA", "Europe/Kiev"),
        ("45.99194", "29.41824", "Artsyz", "UA", "Europe/Kiev"),
        ("2.41669", "30.98551", "Paidha", "UG", "Africa/Kampala"),
        ("3.27833", "32.88667", "Kitgum", "UG", "Africa/Kampala"),
        ("3.02013", "30.91105", "Arua", "UG", "Africa/Kampala"),
        ("33.45122", "-86.99666", "Hueytown", "US", "America/Chicago"),
        ("33.44872", "-86.78777", "Vestavia Hills", "US", "America/Chicago"),
        ("35.25064", "-91.73625", "Searcy", "US", "America/Chicago"),
        ("26.68451", "-80.66756", "Belle Glade", "US", "America/New_York"),
        ("28.54944", "-81.77285", "Clermont", "US", "America/New_York"),
        ("28.90054", "-81.26367", "Deltona", "US", "America/New_York"),
        ("29.65163", "-82.32483", "Gainesville", "US", "America/New_York"),
        ("25.67927", "-80.31727", "Kendall", "US", "America/New_York"),
        ("28.15112", "-82.46148", "Lutz", "US", "America/New_York"),
        ("26.2173", "-80.22588", "North Lauderdale", "US", "America/New_York"),
        ("30.17746", "-81.38758", "Palm Valley", "US", "America/New_York"),
        ("26.91756", "-82.07842", "Punta Gorda Isles", "US", "America/New_York"),
        ("27.71809", "-82.35176", "Sun City Center", "US", "America/New_York"),
        ("27.09978", "-82.45426", "Venice", "US", "America/New_York"),
        ("34.06635", "-84.67837", "Acworth", "US", "America/New_York"),
        ("32.54044", "-82.90375", "Dublin", "US", "America/New_York"),
        ("33.08014", "-83.2321", "Milledgeville", "US", "America/New_York"),
        ("33.54428", "-84.23381", "Stockbridge", "US", "America/New_York"),
        ("38.58894", "-89.99038", "Fairview Heights", "US", "America/Chicago"),
        ("39.78504", "-85.76942", "Greenfield", "US", "America/Indiana/Indianapolis"),
        ("38.06084", "-97.92977", "Hutchinson", "US", "America/Chicago"),
        ("39.08367", "-84.50855", "Covington", "US", "America/New_York"),
        ("36.61033", "-88.31476", "Murray", "US", "America/Chicago"),
        ("29.84576", "-90.10674", "Estelle", "US", "America/Chicago"),
        ("32.52515", "-93.75018", "Shreveport", "US", "America/Chicago"),
        ("38.96372", "-76.99081", "Chillum", "US", "America/New_York"),
        ("38.70734", "-77.02303", "Fort Washington", "US", "America/New_York"),
        ("39.33427", "-76.43941", "Middle River", "US", "America/New_York"),
        ("39.32011", "-76.51552", "Rosedale", "US", "America/New_York"),
        ("39.32288", "-76.72803", "Woodlawn", "US", "America/New_York"),
        ("39.09112", "-94.41551", "Independence", "US", "America/Chicago"),
        ("37.95143", "-91.77127", "Rolla", "US", "America/Chicago"),
        ("33.41012", "-91.06177", "Greenville", "US", "America/Chicago"),
        ("34.25807", "-88.70464", "Tupelo", "US", "America/Chicago"),
        ("35.05266", "-78.87836", "Fayetteville", "US", "America/New_York"),
        ("34.25628", "-78.04471", "Leland", "US", "America/New_York"),
        ("35.88264", "-80.08199", "Thomasville", "US", "America/New_York"),
        ("39.71734", "-74.96933", "Sicklerville", "US", "America/New_York"),
        ("39.43534", "-84.20299", "Lebanon", "US", "America/New_York"),
        ("34.77453", "-96.67834", "Ada", "US", "America/Chicago"),
        ("35.74788", "-95.36969", "Muskogee", "US", "America/Chicago"),
        ("39.96097", "-75.60804", "West Chester", "US", "America/New_York"),
        ("33.98154", "-81.23621", "Lexington", "US", "America/New_York"),
        ("36.02506", "-86.77917", "Brentwood Estates", "US", "America/Chicago"),
        ("35.61452", "-88.81395", "Jackson", "US", "America/Chicago"),
        ("32.44874", "-99.73314", "Abilene", "US", "America/Chicago"),
        ("30.16688", "-96.39774", "Brenham", "US", "America/Chicago"),
        ("31.12406", "-97.90308", "Copperas Cove", "US", "America/Chicago"),
        ("29.53885", "-95.44744", "Fresno", "US", "America/Chicago"),
        ("30.5427", "-97.54667", "Hutto", "US", "America/Chicago"),
        ("32.5007", "-94.74049", "Longview", "US", "America/Chicago"),
        ("31.76212", "-95.63079", "Palestine", "US", "America/Chicago"),
        ("26.18924", "-98.15529", "San Juan", "US", "America/Chicago"),
        ("32.35126", "-95.30106", "Tyler", "US", "America/Chicago"),
        ("37.52487", "-77.55777", "Bon Air", "US", "America/New_York"),
        ("38.91817", "-78.19444", "Front Royal", "US", "America/New_York"),
        ("37.60876", "-77.37331", "Mechanicsville", "US", "America/New_York"),
        ("39.00622", "-77.4286", "Sterling", "US", "America/New_York"),
        ("39.45621", "-77.96389", "Martinsburg", "US", "America/New_York"),
        ("41.27621", "-72.86843", "East Haven", "US", "America/New_York"),
        ("41.14676", "-73.49484", "New Canaan", "US", "America/New_York"),
        ("41.55815", "-73.0515", "Waterbury", "US", "America/New_York"),
        ("41.6764", "-91.58045", "Coralville", "US", "America/Chicago"),
        ("41.57721", "-93.71133", "West Des Moines", "US", "America/Chicago"),
        ("41.15376", "-87.88754", "Bourbonnais", "US", "America/Chicago"),
        ("42.24113", "-88.3162", "Crystal Lake", "US", "America/Chicago"),
        ("41.72059", "-87.70172", "Evergreen Park", "US", "America/Chicago"),
        ("42.16808", "-88.42814", "Huntley", "US", "America/Chicago"),
        ("41.8542", "-87.66561", "Lower West Side", "US", "America/Chicago"),
        ("41.80753", "-87.65644", "New City", "US", "America/Chicago"),
        ("40.56754", "-89.64066", "Pekin", "US", "America/Chicago"),
        ("41.84364", "-87.71255", "South Lawndale", "US", "America/Chicago"),
        ("41.85059", "-87.882", "Westchester", "US", "America/Chicago"),
        ("41.75338", "-86.11084", "Granger", "US", "America/Indiana/Indianapolis"),
        ("41.47892", "-87.45476", "Schererville", "US", "America/Chicago"),
        ("42.35843", "-71.05977", "Boston", "US", "America/New_York"),
        ("42.58342", "-71.8023", "Fitchburg", "US", "America/New_York"),
        ("42.4251", "-71.06616", "Malden", "US", "America/New_York"),
        ("42.52787", "-70.92866", "Peabody", "US", "America/New_York"),
        ("41.9001", "-71.08977", "Taunton", "US", "America/New_York"),
        ("43.91452", "-69.96533", "Brunswick", "US", "America/New_York"),
        ("42.30865", "-83.48216", "Canton", "US", "America/Detroit"),
        ("46.09273", "-88.64235", "Iron River", "US", "America/Menominee"),
        ("42.97086", "-82.42491", "Port Huron", "US", "America/Detroit"),
        ("42.7392", "-84.62081", "Waverly", "US", "America/Detroit"),
        ("45.0408", "-93.263", "Columbia Heights", "US", "America/Chicago"),
        ("45.16024", "-93.08883", "Lino Lakes", "US", "America/Chicago"),
        ("44.73941", "-93.12577", "Rosemount", "US", "America/Chicago"),
        ("47.92526", "-97.03285", "Grand Forks", "US", "America/Chicago"),
        ("42.93369", "-72.27814", "Keene", "US", "America/New_York"),
        ("40.94065", "-73.99681", "Dumont", "US", "America/New_York"),
        ("40.72816", "-74.07764", "Jersey City", "US", "America/New_York"),
        ("40.82232", "-74.15987", "Nutley", "US", "America/New_York"),
        ("40.65538", "-74.38987", "Scotch Plains", "US", "America/New_York"),
        ("40.5576", "-74.28459", "Woodbridge", "US", "America/New_York"),
        ("40.57788", "-73.95958", "Brighton Beach", "US", "America/New_York"),
        ("40.67705", "-73.89125", "Cypress Hills", "US", "America/New_York"),
        ("40.60538", "-73.75513", "Far Rockaway", "US", "America/New_York"),
        ("40.72371", "-73.95097", "Greenpoint", "US", "America/New_York"),
        ("40.64621", "-73.97069", "Kensington", "US", "America/New_York"),
        ("40.68066", "-73.47429", "Massapequa", "US", "America/New_York"),
        ("41.50343", "-74.01042", "Newburgh", "US", "America/New_York"),
        ("40.63316", "-74.13653", "Port Richmond", "US", "America/New_York"),
        ("41.0051", "-73.78458", "Scarsdale", "US", "America/New_York"),
        ("43.1009", "-75.23266", "Utica", "US", "America/New_York"),
        ("40.93121", "-73.89875", "Yonkers", "US", "America/New_York"),
        ("41.55838", "-81.56929", "Collinwood", "US", "America/New_York"),
        ("41.48199", "-81.79819", "Lakewood", "US", "America/New_York"),
        ("41.24255", "-82.61573", "Norwalk", "US", "America/New_York"),
        ("41.66394", "-83.55521", "Toledo", "US", "America/New_York"),
        ("40.2737", "-76.88442", "Harrisburg", "US", "America/New_York"),
        ("40.24537", "-75.64963", "Pottstown", "US", "America/New_York"),
        ("41.54566", "-71.29144", "Middletown", "US", "America/New_York"),
        ("43.61062", "-72.97261", "Rutland", "US", "America/New_York"),
        ("44.27804", "-88.27205", "Kaukauna", "US", "America/Chicago"),
        ("42.55308", "-87.93341", "Pleasant Prairie", "US", "America/Chicago"),
        ("41.16704", "-73.20483", "Bridgeport", "US", "America/New_York"),
        ("33.35283", "-111.78903", "Gilbert", "US", "America/Phoenix"),
        ("33.50921", "-111.89903", "Scottsdale", "US", "America/Phoenix"),
        ("38.17492", "-122.2608", "American Canyon", "US", "America/Los_Angeles"),
        ("33.92946", "-116.97725", "Beaumont", "US", "America/Los_Angeles"),
        ("34.21639", "-119.0376", "Camarillo", "US", "America/Los_Angeles"),
        ("34.09668", "-117.71978", "Claremont", "US", "America/Los_Angeles"),
        ("38.54491", "-121.74052", "Davis", "US", "America/Los_Angeles"),
        ("33.03699", "-117.29198", "Encinitas", "US", "America/Los_Angeles"),
        ("34.14251", "-118.25508", "Glendale", "US", "America/Los_Angeles"),
        ("33.7207", "-116.21677", "Indio", "US", "America/Los_Angeles"),
        ("33.52253", "-117.70755", "Laguna Niguel", "US", "America/Los_Angeles"),
        ("34.63915", "-120.45794", "Lompoc", "US", "America/Los_Angeles"),
        ("32.9156", "-117.14392", "Mira Mesa", "US", "America/Los_Angeles"),
        ("33.93113", "-117.54866", "Norco", "US", "America/Los_Angeles"),
        ("33.72255", "-116.37697", "Palm Desert", "US", "America/Los_Angeles"),
        ("36.06523", "-119.01677", "Porterville", "US", "America/Los_Angeles"),
        ("37.73604", "-120.93549", "Riverbank", "US", "America/Los_Angeles"),
        ("34.09611", "-118.10583", "San Gabriel", "US", "America/Los_Angeles"),
        ("34.95303", "-120.43572", "Santa Maria", "US", "America/Los_Angeles"),
        ("33.95015", "-118.03917", "South Whittier", "US", "America/Los_Angeles"),
        ("33.76446", "-117.79394", "North Tustin", "US", "America/Los_Angeles"),
        ("36.91023", "-121.75689", "Watsonville", "US", "America/Los_Angeles"),
        ("39.72943", "-104.83192", "Aurora", "US", "America/Denver"),
        ("39.57582", "-105.11221", "Ken Caryl", "US", "America/Denver"),
        ("32.42067", "-104.22884", "Carlsbad", "US", "America/Denver"),
        ("36.20829", "-115.98391", "Pahrump", "US", "America/Los_Angeles"),
        ("31.84568", "-102.36764", "Odessa", "US", "America/Chicago"),
        ("40.58654", "-122.39168", "Redding", "US", "America/Los_Angeles"),
        ("43.54072", "-116.56346", "Nampa", "US", "America/Boise"),
        ("45.49428", "-122.86705", "Aloha", "US", "America/Los_Angeles"),
        ("44.99012", "-123.02621", "Keizer", "US", "America/Los_Angeles"),
        ("45.53929", "-122.38731", "Troutdale", "US", "America/Los_Angeles"),
        ("40.65995", "-111.99633", "Kearns", "US", "America/Denver"),
        ("40.34912", "-111.90466", "Saratoga Springs", "US", "America/Denver"),
        ("47.76232", "-122.2054", "Bothell", "US", "America/Los_Angeles"),
        ("47.38093", "-122.23484", "Kent", "US", "America/Los_Angeles"),
        ("47.64995", "-117.23991", "Opportunity", "US", "America/Los_Angeles"),
        ("46.32374", "-120.00865", "Sunnyside", "US", "America/Los_Angeles"),
        ("20.88953", "-156.47432", "Kahului", "US", "Pacific/Honolulu"),
        ("40.81", "-73.9625", "Morningside Heights", "US", "America/New_York"),
        ("43.16547", "-77.70066", "Gates-North Gates", "US", "America/New_York"),
        ("47.4943", "-122.24092", "Bryn Mawr-Skyway", "US", "America/Los_Angeles"),
        ("47.80527", "-122.24064", "Bothell West", "US", "America/Los_Angeles"),
        ("37.71715", "-122.40433", "Visitacion Valley", "US", "America/Los_Angeles"),
        ("-33.38056", "-56.52361", "Durazno", "UY", "America/Montevideo"),
        ("41.29444", "69.67639", "Parkent", "UZ", "Asia/Tashkent"),
        ("40.11583", "67.84222", "Jizzax", "UZ", "Asia/Samarkand"),
        ("40.78206", "72.34424", "Andijon", "UZ", "Asia/Tashkent"),
        ("9.91861", "-68.30472", "Tinaquillo", "VE", "America/Caracas"),
        ("10.22677", "-67.33122", "La Victoria", "VE", "America/Caracas"),
        ("8.35122", "-62.64102", "Ciudad Guayana", "VE", "America/Caracas"),
        ("8.62261", "-70.20749", "Barinas", "VE", "America/Caracas"),
        ("10.29085", "105.75635", "Sa Dec", "VN", "Asia/Ho_Chi_Minh"),
        ("-17.73648", "168.31366", "Port-Vila", "VU", "Pacific/Efate"),
        ("42.62833", "20.89389", "Glogovac", "XK", "Europe/Belgrade"),
        ("14.53767", "46.83187", "Ataq", "YE", "Asia/Aden"),
        ("-27.76952", "30.79165", "Vryheid", "ZA", "Africa/Johannesburg"),
        ("-26.93366", "29.24152", "Standerton", "ZA", "Africa/Johannesburg"),
        ("-24.19436", "29.00974", "Mokopane", "ZA", "Africa/Johannesburg"),
    )

    def coordinate(self, center: Optional[float] = None, radius: Union[float, int] = 0.001) -> Decimal:
        """
        Optionally center the coord and pick a point within radius.
        """
        if center is None:
            return Decimal(str(self.generator.random.randint(-180000000, 180000000) / 1000000)).quantize(
                Decimal(".000001"),
            )
        else:
            center = float(center)
            radius = float(radius)
            geo = self.generator.random.uniform(center - radius, center + radius)
            return Decimal(str(geo)).quantize(Decimal(".000001"))

    def latitude(self) -> Decimal:
        # Latitude has a range of -90 to 90, so divide by two.
        return self.coordinate() / 2

    def longitude(self) -> Decimal:
        return self.coordinate()

    def latlng(self) -> Tuple[Decimal, Decimal]:
        return (self.latitude(), self.longitude())

    def local_latlng(
        self,
        country_code: str = "US",
        coords_only: bool = False,
    ) -> Optional[Tuple[str, ...]]:
        """Returns a location known to exist on land in a country specified by `country_code`.
        Defaults to 'en_US'. See the `land_coords` list for available locations/countries.
        """
        results = [loc for loc in self.land_coords if loc[3] == country_code]
        if results:
            place: PlaceType = self.random_element(results)
            return (place[0], place[1]) if coords_only else place
        return None

    def location_on_land(self, coords_only: bool = False) -> Tuple[str, ...]:
        """Returns a random tuple specifying a coordinate set guaranteed to exist on land.
        Format is `(latitude, longitude, place name, two-letter country code, timezone)`
        Pass `coords_only` to return coordinates without metadata.
        """
        place: PlaceType = self.random_element(self.land_coords)
        return (place[0], place[1]) if coords_only else place
